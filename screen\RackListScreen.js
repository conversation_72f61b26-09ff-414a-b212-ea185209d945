import {Text} from 'react-native-fast-text';
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  PermissionsAndroid,
  useWindowDimensions,
  Platform,
  ActivityIndicator,
  KeyboardAvoidingView,
  Modal as ModalComponent,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import AIcon from 'react-native-vector-icons/AntDesign';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from 'react-native-modal-datetime-picker';
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import RNFetchBlob from 'rn-fetch-blob';
import {
  isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
} from '../util/common';
import {CommonStore} from '../store/commonStore';
import {
  EMAIL_REPORT_TYPE,
  PURCHASE_ORDER_STATUS,
  EXPAND_TAB_TYPE,
  STOCK_STATUS,
  STOCK_STATUS_PARSED,
} from '../constant/common';
import {UserStore} from '../store/userStore';
import {MerchantStore} from '../store/merchantStore';
import {
  convertArrayToCSV,
  autofitColumns,
  getImageFromFirebaseStorage,
  getPathForFirebaseStorageFromBlob,
  uploadFileToFirebaseStorage,
  generateEmailReport,
  parseValidPriceText,
} from '../util/common';
import {useKeyboard} from '../hooks';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import SupplierScreen from './SupplierProductScreen';
// import RNPickerSelect from 'react-native-picker-select';
import XLSX from 'xlsx';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {zip, unzip, unzipAssets, subscribe} from 'react-native-zip-archive';
import 'react-native-get-random-values';
import {v4 as uuidv4} from 'uuid';
import {OutletStore} from '../store/outletStore';
import AsyncImage from '../components/asyncImage';
// import { fi, fil } from 'date-fns/locale';
import {useFocusEffect} from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal, {createRack} from '../util/apiLocalReplacers';
import {logEventAnalytics} from '../util/common';
import {ANALYTICS, ANALYTICS_PARSED} from '../constant/analytics';
import Tooltip from 'react-native-walkthrough-tooltip';
import RadioForm, {
  RadioButton,
  RadioButtonInput,
  RadioButtonLabel,
} from 'react-native-simple-radio-button';
import {FlashList} from '@shopify/flash-list';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  EFFECTIVE_DAY_DROPDOWN_LIST1,
  EFFECTIVE_DAY,
} from '../constant/promotions';
import QRCode from 'react-native-qrcode-svg';
const RNFS = require('@dr.pogodin/react-native-fs');

const InventoryProductScreen = props => {
  const {navigation} = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const {width: windowWidth, height: windowHeight} = useWindowDimensions();

  const [productUsingSupplyItem, setProductUsingSupplyItem] = useState(false);
  const [lowStockAlert, setLowStockAlert] = useState(true);
  const [purchaseOrder, setPurchaseOrder] = useState(true);
  const [stockTransfer, setStockTransfer] = useState(false);
  const [stockTake, setStockTake] = useState(false);
  const [addStockTake, setAddStockTake] = useState(false);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}]);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}]);
  const [addStockTransferList, setAddStockTransferList] = useState([
    {},
    {},
    {},
  ]);
  const [switchMerchant, setSwitchMerchant] = useState(
    isTablet() ? false : true,
  );

  const [search, setSearch] = useState('');

  const [loading, setLoading] = useState(false);

  const [selectedOutletSupplyItem, setSelectedOutletSupplyItem] = useState({});
  const [inUsedOutletItems, setInUsedOutletItems] = useState([]);

  const [exportModalVisibility, setExportModalVisibility] = useState(false);
  const [exportEmail, setExportEmail] = useState('');

  const [temp, setTemp] = useState('');

  const [totalLowStockQuantity, setTotalLowStockQuantity] = useState(0);
  ///////////////////////////////////////////////////////

  const [skuOrderQuantityDict, setSkuOrderQuantityDict] = useState({});
  const [skuEstimatedArrivalDateDict, setSkuEstimatedArrivalDateDict] =
    useState({});

  const [inputStockIdealDict, setInputStockIdealDict] = useState({});
  const [inputStockWarningDict, setInputStockWarningDict] = useState({});

  const outletSupplyItems = CommonStore.useState(s => s.outletSupplyItems);

  const supplyItemsSkuDict = CommonStore.useState(s => s.supplyItemsSkuDict);
  const suppliersProductDict = CommonStore.useState(
    s => s.suppliersProductDict,
  );

  const outletCategoriesDict = OutletStore.useState(
    s => s.outletCategoriesDict,
  );

  const purchaseOrdersProduct = CommonStore.useState(
    s => s.purchaseOrdersProduct,
  );

  const isLoading = CommonStore.useState(s => s.isLoading);

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);
  const [isLoadingLocalExcel, setIsLoadingLocalExcel] = useState(false);
  const [isLoadingLocalCsv, setIsLoadingLocalCsv] = useState(false);

  const firebaseUid = UserStore.useState(s => s.firebaseUid);
  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);

  const currOutletId = MerchantStore.useState(s => s.currOutletId);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);

  const outletItems = OutletStore.useState(s => s.outletItems);
  const outletItemsDict = OutletStore.useState(s => s.outletItemsDict);

  const isAlphaUser = UserStore.useState(s => s.isAlphaUser);
  const expandTab = CommonStore.useState(s => s.expandTab);
  const currPageStack = CommonStore.useState(s => s.currPageStack);

  const [selectedStockStatus, setSelectedStockStatus] = useState('ALL');
  const [filteredOutletItems, setFilteredOutletItems] = useState([]);
  const stockStatus = [
    {label: 'ALL', value: 0},
    {label: 'Below Ideal Stock', value: 1},
    {label: 'Below Warning Stock', value: 2},
  ];
  const [editCategory, setEditCategory] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [hideModal, setHideModal] = useState(false);
  const [hideHelp, setHideHelp] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState(null);
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState(null);
  const [deleteOn, setDeleteOn] = useState(false);
  const [changeLayout, setChangeLayout] = useState(false);
  const outletSections = OutletStore.useState(s => s.outletSections);
  const [categoryUniqueId, setCategoryUniqueId] = useState('');
  const [hideInOrderTypes, setHideInOrderTypes] = useState('');
  const [isAvailableDayActive, setIsAvailableDayActive] = useState(false);
  const [printKDNum, setPrintKDNum] = useState(1);
  const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] =
    useState([EFFECTIVE_DAY_DROPDOWN_LIST1[0].value]);
  const [selectedHideOutletSectionIdList, setSelectedHideOutletSectionIdList] =
    useState([]);
  const [outletSectionDropdownList, setOutletSectionDropdownList] = useState(
    [],
  );

  const [showQRModal, setShowQRModal] = useState(false);
  const [rackName, setRackName] = useState('');
  const selectedRackEdit = CommonStore.useState(s => s.selectedRackEdit);
  const [poItems, setPoItems] = useState([
    {
      supplyItemId: '',
      name: '',
      sku: '',
      skuMerchant: '',
      quantity: 0,
      unit: '',
      // orderQuantity: 0,
      // receivedQuantity: 0,
      // price: 0,
      // totalPrice: 0,
      insertQuantity: 0,
      supplyItem: null,
      bc: '',
    },
  ]);

  useEffect(() => {
    var filteredOutletItemsTemp = [];

    if (selectedStockStatus === STOCK_STATUS.ALL) {
      for (var i = 0; i < outletItems.length; i++) {
        filteredOutletItemsTemp.push(outletItems[i]);
      }
    }

    if (selectedStockStatus === STOCK_STATUS.BELOW_IDEAL_STOCK) {
      for (var i = 0; i < outletItems.length; i++) {
        if (outletItems[i].stockCount <= outletItems[i].stockIdealQuantity) {
          // console.log('ideal++');
          filteredOutletItemsTemp.push(outletItems[i]);
        }
      }
    }

    if (selectedStockStatus === STOCK_STATUS.BELOW_WARNING_STOCK) {
      for (var i = 0; i < outletItems.length; i++) {
        if (outletItems[i].stockCount <= outletItems[i].stockWarningQuantity) {
          // console.log('warning++');
          filteredOutletItemsTemp.push(outletItems[i]);
        }
      }
    }

    setFilteredOutletItems(filteredOutletItemsTemp);
  }, [outletItems, selectedStockStatus]);

  useEffect(() => {
    if (selectedRackEdit) {
      // insert info

      setCategoryUniqueId(selectedRackEdit.uniqueId);
      setRackName(selectedRackEdit.rackName);
      setPoItems(
        Object.values(selectedRackEdit.items || {}).map(item => ({
          supplyItemId: item.id,
          name: item.n,
          sku: item.sku,
          skuMerchant: item.skuM,
          quantity: item.sc,
          unit: '',
          // orderQuantity: 0,
          // receivedQuantity: 0,
          // price: 0,
          // totalPrice: 0,
          insertQuantity: 0,
          // supplyItem: null,
          bc: item.bc ? item.bc : '',
        })),
      );
      // setHideInOrderTypes(selectedRackEdit.hideInOrderTypes || []);
      // setIsAvailableDayActive(selectedRackEdit.isAvailableDayActive || false,);

      // setPrintKDNum(selectedRackEdit.printKDNum ? selectedRackEdit.printKDNum : 1);

      // var effectiveTypeOptionsTemp = [];

      // if (selectedRackEdit && selectedRackEdit.effectiveTypeOptions &&
      //     selectedRackEdit.effectiveTypeOptions.length > 0) {
      //     for (var i = 0; i < selectedRackEdit.effectiveTypeOptions.length; i++) {
      //         if (
      //             EFFECTIVE_DAY_DROPDOWN_LIST1.find(
      //                 (item) => item.value === selectedRackEdit.effectiveTypeOptions[i],
      //             )
      //         ) {
      //             effectiveTypeOptionsTemp.push(selectedRackEdit.effectiveTypeOptions[i]);
      //         }
      //     }
      //     if (selectedRackEdit.isAvailableDayActive === true) {
      //         setSelectedEffectiveTypeOptions(effectiveTypeOptionsTemp);
      //     }
      // }

      // setSelectedHideOutletSectionIdList(selectedRackEdit.hideOutletSectionIdList ? selectedRackEdit.hideOutletSectionIdList : []);
    } else {
      // designed to always mounted, thus need clear manually...

      setCategoryUniqueId('');
      setRackName('');
      setHideInOrderTypes([]);
      setSelectedHideOutletSectionIdList([]);

      setPrintKDNum(1);

      setPoItems([
        ...poItems,
        {
          supplyItemId: outletItems[0].uniqueId,
          name: outletItems[0].name,
          sku: outletItems[0].sku,
          skuMerchant: outletItems[0].skuMerchant,
          unit: '',
          quantity: outletItems[0].stockCount || 0, // check if the supply item sku for this outlet existed
          insertQuantity: 0,
          supplyItem: supplyItems[0],
          bc: outletItems[0].bc ? outletItems[0].bc : '',
        },
      ]);
    }
  }, [selectedRackEdit]);

  useEffect(() => {
    const outletSectionDropdownListTemp = outletSections.map(item => ({
      label: item.sectionName,
      // value: item.uniqueId,
      value: item.uniqueId,
    }));

    setOutletSectionDropdownList(outletSectionDropdownListTemp);

    if (selectedRackEdit === null) {
      console.log('========================================');
      console.log('null product');
      console.log(outletSectionDropdownListTemp.length);

      if (outletSectionDropdownListTemp.length > 0) {
        console.log('help select');

        // setSelectedHideOutletSectionIdList(outletSectionDropdownListTemp);
        setSelectedHideOutletSectionIdList([]);
      } else {
        console.log('make blank');

        setSelectedHideOutletSectionIdList([]);
      }
    } else {
      var selectedOutletSectionListTemp = [];

      if (
        selectedRackEdit.hideOutletSectionIdList &&
        selectedRackEdit.hideOutletSectionIdList.length > 0
      ) {
        for (
          var i = 0;
          i < selectedRackEdit.hideOutletSectionIdList.length;
          i++
        ) {
          var isOutletSectionMissing = true;

          const outletSectionId = selectedRackEdit.hideOutletSectionIdList[i];

          for (var j = 0; j < outletSectionDropdownListTemp.length; j++) {
            if (outletSectionDropdownListTemp[j].value === outletSectionId) {
              isOutletSectionMissing = false;
              break;
            }
          }

          if (!isOutletSectionMissing) {
            // means the saved printer area deleted or renamed
            // skip the printer area
            selectedOutletSectionListTemp.push(outletSectionId);
          } else {
          }
        }
      }

      setSelectedHideOutletSectionIdList(selectedOutletSectionListTemp);
    }
  }, [outletSections, selectedRackEdit]);

  useEffect(() => {
    var inUsedOutletItemsTemp = [];

    if (selectedOutletSupplyItem && selectedOutletSupplyItem.uniqueId) {
      for (var i = 0; i < outletItems.length; i++) {
        if (outletItems[i].stockLinkItems) {
          for (var j = 0; j < outletItems[i].stockLinkItems.length; j++) {
            if (
              outletItems[i].stockLinkItems[j].sku ===
              selectedOutletSupplyItem.sku
            ) {
              // means got used this outlet supply item

              inUsedOutletItemsTemp.push(outletItems[i]);
            }
          }
        }
      }
    }

    setInUsedOutletItems(inUsedOutletItemsTemp);
  }, [selectedOutletSupplyItem, outletItems]);

  useEffect(() => {
    var skuOrderQuantityDictTemp = {};
    var skuEstimatedArrivalDateDictTemp = {};

    var purchaseOrdersActive = purchaseOrdersProduct.filter(
      po => po.status !== PURCHASE_ORDER_STATUS.COMPLETED,
    );

    for (var i = 0; i < purchaseOrdersActive.length; i++) {
      for (var j = 0; j < purchaseOrdersActive[i].poItems.length; j++) {
        const record = purchaseOrdersActive[i].poItems[j];

        if (skuOrderQuantityDictTemp[record.sku] === undefined) {
          skuOrderQuantityDictTemp[record.sku] = record.orderQuantity;
        } else {
          skuOrderQuantityDictTemp[record.sku] += record.orderQuantity;
        }

        skuEstimatedArrivalDateDictTemp[record.sku] =
          record.estimatedArrivalDate;
      }
    }

    setSkuOrderQuantityDict(skuOrderQuantityDictTemp);
    setSkuEstimatedArrivalDateDict(skuEstimatedArrivalDateDictTemp);
  }, [purchaseOrdersProduct]);

  useEffect(() => {
    requestStoragePermission();
  }, []);

  ///////////////////////////////////////////////////////

  // add item screen

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [supplyItemDropdownList, setSupplyItemDropdownList] = useState([]);

  const outletSupplyItemsSkuDict = CommonStore.useState(
    s => s.outletSupplyItemsSkuDict,
  );

  const supplyItems = CommonStore.useState(s => s.supplyItems);
  const supplyItemsDict = CommonStore.useState(s => s.supplyItemsDict);
  const suppliersProduct = CommonStore.useState(s => s.suppliersProduct);
  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);
  const merchantLogo = MerchantStore.useState(s => s.logo);

  const outletSelectDropdownView = CommonStore.useState(
    s => s.outletSelectDropdownView,
  );

  const hideCategoryChoice = [
    // {
    //   label: '2 HOURS',
    //   value: 'HOURS_2',
    // },
    // {
    //   label: '6 HOURS',
    //   value: 'HOURS_6',
    // },
    // {
    //   label: 'END OF THE DAY',
    //   value: 'END_DAY',
    // },
    // {
    //   label: 'CUSTOM TIME',
    //   value: 'CUSTOM_TIME',
    // },
    // {
    //   label: 'FOREVER',
    //   value: 'FOREVER',
    // },
  ];

  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map(outlet => ({
        label: outlet.name,
        value: outlet.uniqueId,
      })),
    );

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setSupplyItemDropdownList(
      outletItems.map(supplyItem => {
        // if (selectedSupplierId === supplyItem.supplierId) {
        //   return { label: supplyItem.name, value: supplyItem.uniqueId };
        // }

        return {
          label: supplyItem.name,
          value: supplyItem.uniqueId,
        };
      }),
    );

    if (
      outletItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].supplyItemId === ''
    ) {
      setPoItems([
        {
          supplyItemId: outletItems[0].uniqueId,
          name: outletItems[0].name,
          sku: outletItems[0].sku,
          skuMerchant: outletItems[0].skuMerchant,
          // quantity: outletSupplyItemsSkuDict[supplyItems[0].sku]
          //   ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity
          //   : 0, // check if the supply item sku for this outlet existed
          quantity: outletItems[0].stockCount || 0,
          unit: '',
          // orderQuantity: 0,
          // receivedQuantity: 0,
          // price: supplyItems[0].price,
          // totalPrice: 0,
          insertQuantity: 0,
          supplyItem: outletItems[0],
          bc: outletItems[0].bc ? outletItems[0].bc : '',
        },
      ]);
    } else if (
      poItems[0].supplyItemId !== '' &&
      // Object.keys(outletItemsDict).length > 0
      outletItems.length > 0
    ) {
      var poItemsTemp = [...poItems];

      for (var i = 0; i < poItemsTemp.length; i++) {
        let foundItem = outletItems.find(
          findItem => findItem.uniqueId === poItemsTemp[i].supplyItemId,
        );

        const supplyItem = foundItem;

        poItemsTemp[i] = {
          ...poItemsTemp[i],
          // quantity: outletSupplyItemsSkuDict[supplyItem.sku]
          //   ? outletSupplyItemsSkuDict[supplyItem.sku].quantity
          //   : 0, // check if the supply item sku for this outlet existed | might changed in real time
          quantity:
            supplyItem && supplyItem.stockCount ? supplyItem.stockCount : 0,
          // price: supplyItem.price, // might changed in real time
        };
      }

      setPoItems(poItemsTemp);
    }
  }, [
    // supplyItems,
    // supplyItemsDict,
    // outletSupplyItemsSkuDict,
    outletItems,
    outletItemsDict,
  ]);

  ///////////////////////////////////////////////////////

  const setState = () => {};

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update(s => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          } else {
            navigation.navigate('Table');

            CommonStore.update(s => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update(s => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[
            {
              width: 124,
              height: 26,
            },
            switchMerchant
              ? {
                  transform: [{scaleX: 0.7}, {scaleY: 0.7}],
                }
              : {},
          ]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            marginRight: Platform.OS === 'ios' ? '27%' : 0,
            bottom: switchMerchant ? '2%' : 0,
            width: switchMerchant
              ? '100%'
              : Platform.OS === 'ios'
              ? '96%'
              : '55%',
          },
          windowWidth >= 768 && switchMerchant
            ? {right: windowWidth * 0.1}
            : {},
          windowWidth <= 768 ? {right: 20} : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? '1%' : 0,
          }}>
          Inventory
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: Dimensions.get('screen').height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}
        />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text
            style={[
              {
                fontFamily: 'NunitoSans-SemiBold',
                fontSize: switchMerchant ? 10 : 16,
                color: Colors.secondaryColor,
                marginRight: 15,
              },
              switchMerchant ? {width: windowWidth / 8} : {},
            ]}
            numberOfLines={switchMerchant ? 1 : 1}>
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: Dimensions.get('screen').height * 0.05,
              height: Dimensions.get('screen').height * 0.05,
              borderRadius: Dimensions.get('screen').height * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: Dimensions.get('screen').height * 0.035,
                height: Dimensions.get('screen').height * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder()
  //   getStockTransfer()
  //   getLowStock()
  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'KooDoo Retail Storage Permission',
          message: 'KooDoo Retail App needs access to your storage ',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log('Storage permission granted');
      } else {
        // console.log('Storage permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    // var allOutletsStr = allOutlets.map(item => item.name).join(';');
    var allOutletsStr = allOutlets
      .map(
        item =>
          `${item.name}:${Math.floor(Math.random() * (100 - 0 + 1)) + 0}|${
            Math.floor(Math.random() * (100 - 0 + 1)) + 0
          }|${Math.floor(Math.random() * (100 - 0 + 1)) + 0}`,
      )
      .join(';');

    // var taxName = currOutletTaxes[0].name;

    var excelColumn = {
      Name: 'Potato',
      'Outlet Stock (Quantity|Ideal|Warning)': allOutletsStr,
      SKU: 'RP001',
      Description: 'Fresh potato',
      Price: '1.99',
      Currency: 'MYR',
      Unit: 'kg',
      'Supplier Name': 'Fresh Harvest Sdn Bhd',
      'Supplier Email': '<EMAIL>',
      'Supplier Phone': '60195935968',
      'Supplier Tax Rate': '0.05',
      'Supplier Description': 'Vegetables & fruits supplier',
      'Image Path': 'Potato',
    };
    excelTemplate.push(excelColumn);

    var excelColumn2 = {
      Name: 'Sesame oil',
      'Outlet Stock (Quantity|Ideal|Warning)': allOutletsStr,
      SKU: 'RP002',
      Description: 'Sesame oil',
      Price: '2.99',
      Currency: 'MYR',
      Unit: 'l',
      'Supplier Name': 'Oil Holding Sdn Bhd',
      'Supplier Email': '<EMAIL>',
      'Supplier Phone': '60124591893',
      'Supplier Tax Rate': '0.05',
      'Supplier Description': 'Oils supplier',
      'Image Path': 'Sesame_oil',
    };
    excelTemplate.push(excelColumn2);

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;
  };

  const exportTemplate = async () => {
    try {
      const excelTemplate = convertTemplateToExcelFormat();

      const tempFolderName = `koodoo-inventory-template-${Date.now().toString()}`;
      const tempFolderPath = `${
        Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
      }/${tempFolderName}`;

      const tempImageFolderName = 'images/Potato';
      const tempImageFolderPath = `${
        Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
      }/${tempFolderName}/${tempImageFolderName}`;

      const tempImageFolderName2 = 'images/Sesame_oil';
      const tempImageFolderPath2 = `${
        Platform.OS === 'ios'
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath
      }/${tempFolderName}/${tempImageFolderName2}`;

      let exists = await RNFS.exists(tempFolderPath);
      if (exists) {
        // exists call delete
        await RNFS.unlink(tempFolderPath);
        // console.log("Previous Temp File Deleted", tempFolderPath);
      } else {
        // console.log("Previous Temp File Not Available", tempFolderPath)
      }

      RNFS.mkdir(tempFolderPath);
      RNFS.mkdir(tempImageFolderPath);
      RNFS.mkdir(tempImageFolderPath2);

      var templateImageUrl = '';

      // download merchant logo as example image file

      if (merchantLogo) {
        await new Promise((resolve, reject) => {
          if (
            merchantLogo.startsWith('http') ||
            merchantLogo.startsWith('file')
          ) {
            templateImageUrl = merchantLogo;

            resolve();
          } else {
            getImageFromFirebaseStorage(merchantLogo, parsedUrl => {
              templateImageUrl = parsedUrl;

              resolve();
            });
          }
        });

        var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
        tempImageFileName = tempImageFileName.split('?')[0];

        const tempImageFilePath = `${
          Platform.OS === 'ios'
            ? RNFS.DocumentDirectoryPath
            : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

        const downloadJob = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath,
        });

        await downloadJob.promise;

        const tempImageFilePath2 = `${
          Platform.OS === 'ios'
            ? RNFS.DocumentDirectoryPath
            : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/${tempImageFolderName2}/${tempImageFileName}`;

        const downloadJob2 = RNFS.downloadFile({
          fromUrl: templateImageUrl,
          toFile: tempImageFilePath2,
        });

        await downloadJob2.promise;

        // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        var excelFile = `${
          Platform.OS === 'ios'
            ? RNFS.DocumentDirectoryPath
            : RNFS.DownloadDirectoryPath
        }/${tempFolderName}/koodoo-inventory-template.xlsx`;
        var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
        var excelWorkBook = XLSX.utils.book_new();

        excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);

        XLSX.utils.book_append_sheet(
          excelWorkBook,
          excelWorkSheet,
          'Inventory Template',
        );

        const workBookData = XLSX.write(excelWorkBook, {
          type: 'binary',
          bookType: 'xlsx',
        });

        RNFS.writeFile(excelFile, workBookData, 'ascii')
          .then(async success => {
            // console.log(`wrote file ${excelFile}`);

            // zip the folder

            const tempZipPath = `${
              Platform.OS === 'ios'
                ? RNFS.DocumentDirectoryPath
                : RNFS.DownloadDirectoryPath
            }/${tempFolderName}.zip`;

            let exists = await RNFS.exists(tempZipPath);
            if (exists) {
              // exists call delete
              await RNFS.unlink(tempZipPath);
              // console.log("Previous Zip File Deleted");
            } else {
              // console.log("Previous Zip File Not Available")
            }

            zip(tempFolderPath, tempZipPath)
              .then(async path => {
                // console.log(`zip completed at ${path}`);

                let exists = await RNFS.exists(tempFolderPath);
                if (exists) {
                  // exists call delete
                  await RNFS.unlink(tempFolderPath);
                  // console.log("Clean Temp folder File Deleted");
                } else {
                  // console.log("Clean Temp folder File Not Available")
                }

                Alert.alert(
                  'Success',
                  `Sent to ${tempZipPath}`,
                  [{text: 'OK', onPress: () => {}}],
                  {cancelable: false},
                );
              })
              .catch(error => {
                console.error(error);

                Alert.alert(
                  'Error',
                  'Failed to export template \n Try to deleting the old file and try again',
                );
              });
          })
          .catch(err => {
            // console.log(err.message);

            Alert.alert(
              'Error',
              'Failed to export template \n Try to deleting the old file and try again',
            );
          });
      } else {
        Alert.alert(
          'Info',
          'Please set the merchant logo first before proceed.',
        );
      }
    } catch (ex) {
      console.error(ex);

      Alert.alert(
        'Error',
        'Failed to export template \n Try to deleting the old file and try again',
      );
    }
  };

  const importTemplateData = async () => {
    CommonStore.update(s => {
      s.isLoading = true;
    });

    try {
      var res = null;
      if (Platform.OS === 'ios') {
        res = await DocumentPicker.pick({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      } else {
        res = await DocumentPicker.pickSingle({
          type: [DocumentPicker.types.zip],
          copyTo: 'documentDirectory',
        });
      }
      // console.log('res');
      // console.log(res);

      // RNFetchBlob.fs.readFile(res.uri, 'base64').then(async data => {
      //   // upload to firebase storage

      //   var referenceId = uuidv4();
      //   var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      //   await uploadFileToFirebaseStorage(data, referencePath);
      // });

      var referenceId = uuidv4();
      var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

      var translatedPath = '';
      if (Platform.OS === 'ios') {
        translatedPath = await getPathForFirebaseStorageFromBlob(res[0]);
      } else {
        translatedPath = await getPathForFirebaseStorageFromBlob(res);
      }

      // console.log('translatedPath');
      // console.log(translatedPath);

      if (Platform.OS === 'ios') {
        if (translatedPath && translatedPath.fileCopyUri) {
          translatedPath = translatedPath.fileCopyUri;
        }
      }

      await uploadFileToFirebaseStorage(translatedPath, referencePath);

      const body = {
        zipId: referenceId,
        zipPath: referencePath,

        userId: firebaseUid,
        merchantId,
        outletId: currOutletId,
      };

      ApiClient.POST(API.batchCreateOutletSupplyItems, body)
        .then(result => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Submitted to upload queue, we will notify you once the process is done',
            );
          } else {
            Alert.alert('Error', 'Failed to import the data');
          }

          CommonStore.update(s => {
            s.isLoading = false;
          });
        })
        .catch(err => {
          // console.log(err);

          Alert.alert('Error', 'Failed to import the data');

          CommonStore.update(s => {
            s.isLoading = false;
          });
        });
    } catch (err) {
      CommonStore.update(s => {
        s.isLoading = false;
      });

      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        console.error(err);

        Alert.alert('Error', 'Failed to import the data');
      }
    }
  };

  //error show readAsArrayBuffer not implemented

  // function here

  //trash icon

  useEffect(() => {
    var totalLowStock = 0;

    for (var i = 0; i < outletItems.length; i++) {
      // console.log('Loop total outletsupply');
      if (
        // (
        // outletItems[i].stockWarningQuantity !== undefined &&
        // outletItems[i].stockCount !== undefined &&
        // outletItems[i].stockWarningQuantity !== undefined) &&
        outletItems[i].stockCount <= outletItems[i].stockWarningQuantity
      ) {
        totalLowStock += 1;
        // console.log('Loop if quantity small than stockidealquantity');
      }
    }
    setTotalLowStockQuantity(totalLowStock);
  }, [
    totalLowStockQuantity,
    // supplyItems,
    // supplyItemsDict,
    // outletSupplyItemsSkuDict,
    outletItems,
    // allOutlets,
  ]);

  const stockTransfersProduct = CommonStore.useState(
    s => s.stockTransfersProduct,
  );
  const stockTakesProduct = CommonStore.useState(s => s.stockTakesProduct);
  const stockReturnsProduct = CommonStore.useState(s => s.stockReturnsProduct);
  const purchaseOrders = CommonStore.useState(s => s.purchaseOrders);
  const [expandViewDict, setExpandViewDict] = useState({});
  const [productHistoryList, setProductHistoryList] = useState([]);
  const [clickedProduct, setClickedProduct] = useState([]);

  /////////////////////////////////////////////

  // 2024-05-24 - rack changes

  const racks = CommonStore.useState(s => s.racks);

  const stockTransfersRackActive = CommonStore.useState(
    s => s.stockTransfersRackActive,
  );

  /////////////////////////////////////////////

  useEffect(() => {
    if (clickedProduct && clickedProduct.sku) {
      var productHistoryListTemp = [];

      for (var i = 0; i < stockTransfersProduct.length; i++) {
        for (var j = 0; j < stockTransfersProduct[i].stItems.length; j++) {
          if (stockTransfersProduct[i].stItems[j].sku === clickedProduct.sku) {
            productHistoryListTemp.push({
              stockAction: stockTransfersProduct[i].stItems[j].transferQuantity,
              stockType:
                stockTransfersProduct[i].targetOutletId === currOutletId
                  ? 'Stock Transfer In'
                  : 'Stock Transfer Out',
              dateTime: stockTransfersProduct[i].createdAt,
            });
          }
        }
      }

      for (var i = 0; i < stockReturnsProduct.length; i++) {
        for (var j = 0; j < stockReturnsProduct[i].stItems.length; j++) {
          if (stockReturnsProduct[i].stItems[j].sku === clickedProduct.sku) {
            productHistoryListTemp.push({
              stockAction: stockReturnsProduct[i].stItems[j].transferQuantity,
              stockType: 'Stock Return',
              dateTime: stockReturnsProduct[i].crseatedAt,
            });
          }
        }
      }

      for (var i = 0; i < stockTakesProduct.length; i++) {
        for (var j = 0; j < stockTakesProduct[i].stItems.length; j++) {
          if (stockTakesProduct[i].stItems[j].sku === clickedProduct.sku) {
            productHistoryListTemp.push({
              stockAction: stockTakesProduct[i].stItems[j].quantity,
              stockType: 'Stock Take',
              dateTime: stockTakesProduct[i].createdAt,
            });
          }
        }
      }

      for (var i = 0; i < purchaseOrdersProduct.length; i++) {
        for (var j = 0; j < purchaseOrdersProduct[i].poItems.length; j++) {
          if (purchaseOrdersProduct[i].poItems[j].sku === clickedProduct.sku) {
            productHistoryListTemp.push({
              stockAction: purchaseOrdersProduct[i].poItems[j].quantity,
              stockType: 'Purchase Order',
              dateTime: purchaseOrdersProduct[i].createdAt,
            });
          }
        }
      }

      productHistoryListTemp.sort((a, b) => b.dateTime - a.dateTime);
      setProductHistoryList(productHistoryListTemp);
    }
  }, [
    clickedProduct,

    stockTransfersProduct,
    stockReturnsProduct,
    stockTakesProduct,
    purchaseOrdersProduct,

    currOutletId,
  ]);

  const expandOrderFunc = param => {
    if (!expandViewDict[param.uniqueId]) {
      // return setState({ expandOrder: true }), param.expandOrder = true;
      // setExpandOrder(true);
      setExpandViewDict({
        ...expandViewDict,
        [param.uniqueId]: true,
      });
    } else {
      // return setState({ expandOrder: false }), param.expandOrder = false;
      // setExpandOrder(false);
      setExpandViewDict({
        ...expandViewDict,
        [param.uniqueId]: false,
      });
    }
  };

  const renderAddPO = () => {
    // becoz poitems don't store supplier id
    if (
      // poItems[index].supplierId === selectedSupplierId
      true
    ) {
      return (
        <>
          {poItems.map((item, index) => {
            return (
              <View
                style={{
                  backgroundColor: '#ffffff',
                  flexDirection: 'row',
                  // paddingVertical: 20,
                  paddingVertical: 5,
                  paddingHorizontal: 20,
                  // borderBottomWidth: StyleSheet.hairlineWidth,
                  // borderBottomColor: '#c4c4c4',
                  alignItems: 'center',
                  zIndex: 10001 + poItems.length - index,
                  // height: (Dimensions.get('window').width * 0.1) * 3,
                }}>
                <View
                  style={{
                    width: '40%',
                    justifyContent: 'center',
                    alignSelf: 'center',
                  }}>
                  {console.log(
                    'supplyItemDropdownList',
                    supplyItemDropdownList,
                  )}
                  {console.log(
                    'supplyItemDropdownList id',
                    poItems[index].supplyItemId,
                  )}

                  {supplyItemDropdownList.length > 0 &&
                    outletItems.length > 0 &&
                    supplyItemDropdownList.find(
                      supplyItemOption =>
                        supplyItemOption.value === poItems[index].supplyItemId,
                    ) && (
                      <DropDownPicker
                        containerStyle={{
                          height: switchMerchant ? 30 : 30,
                          zIndex: 2,
                        }}
                        arrowColor={'black'}
                        arrowSize={20}
                        arrowStyle={{
                          fontWeight: 'bold',
                          marginTop: switchMerchant ? 0 : -3,
                        }}
                        labelStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        style={{
                          width: switchMerchant ? 90 : 150,
                          paddingVertical: 0,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          fontSize: switchMerchant ? 11 : 14,
                        }}
                        placeholderStyle={{color: Colors.fieldtTxtColor}}
                        items={supplyItemDropdownList}
                        itemStyle={{
                          justifyContent: 'flex-start',
                          marginLeft: 5,
                          zIndex: 2,
                        }}
                        placeholder={'Supply Items'}
                        zIndex={10000 + poItems.length - index}
                        //customTickIcon={(press) => <Ionicon name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                        searchable
                        searchableStyle={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.0079,
                        }}
                        defaultValue={
                          poItems[index].supplyItemId
                            ? poItems[index].supplyItemId
                            : ''
                        }
                        onChangeItem={item => {
                          let foundItem = outletItems.find(
                            findItem => findItem.uniqueId === item.value,
                          );

                          setPoItems(
                            poItems.map((poItem, i) =>
                              i === index
                                ? {
                                    ...poItem,
                                    supplyItemId: item.value,
                                    skuMerchant: foundItem.skuMerchant,
                                    sku: foundItem.sku,
                                    // quantity: outletSupplyItemsSkuDict[
                                    //   supplyItemsDict[item.value].sku
                                    // ]
                                    //   ? outletSupplyItemsSkuDict[
                                    //     supplyItemsDict[item.value].sku
                                    //   ].quantity
                                    //   : 0,
                                    name: foundItem.name,
                                    quantity: foundItem.stockCount || 0,
                                    unit: '',
                                    // orderQuantity: 0,
                                    // receivedQuantity: 0,
                                    // price: supplyItemsDict[value].price,
                                    // totalPrice: 0,
                                    insertQuantity: 0,

                                    supplyItem: foundItem,
                                    bc: foundItem.bc ? foundItem.bc : '',
                                  }
                                : poItem,
                            ),
                          );
                        }}
                        dropDownMaxHeight={150}
                        dropDownStyle={{
                          width: switchMerchant ? 90 : 150,
                          height: 150,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 10,
                          borderWidth: 1,
                          textAlign: 'left',
                          zIndex: 2,
                        }}
                      />
                    )}
                </View>

                <Text
                  style={{
                    width: '21%',
                    color: '#8f8f8f',
                    marginLeft: 10,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {poItems[index].skuMerchant || '-'}
                </Text>

                <Text
                  style={{
                    width: '18%',
                    color: '#8f8f8f',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {poItems[index].quantity || 0}
                </Text>

                <View
                  style={{
                    width: '16%',
                    // marginLeft: 50,
                    // backgroundColor: 'blue',
                  }}>
                  <TextInput
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: switchMerchant ? '90%' : 100,
                      height: 35,
                      borderRadius: 5,
                      padding: 5,
                      marginVertical: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      paddingLeft: 10,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                    placeholder={'50'}
                    placeholderStyle={{
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                    placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
                    keyboardType={'decimal-pad'}
                    // placeholder={itemName}
                    //iOS
                    clearTextOnFocus
                    //////////////////////////////////////////////
                    //Android
                    onFocus={() => {
                      setTemp(poItems[index].insertQuantity);
                      setPoItems(
                        poItems.map((poItem, i) =>
                          i === index
                            ? {
                                ...poItem,
                                // orderQuantity: parseInt(text),
                                insertQuantity: '',
                                // totalPrice: parseInt(text) * poItem.price,
                              }
                            : poItem,
                        ),
                      );
                    }}
                    ///////////////////////////////////////////////
                    //When textinput is not selected
                    onEndEditing={() => {
                      if (poItems[index].insertQuantity == '') {
                        if (temp != '') {
                          let add_stock = isNaN(parseInt(temp))
                            ? 0
                            : parseInt(temp);

                          setPoItems(
                            poItems.map((poItem, i) =>
                              i === index
                                ? {
                                    ...poItem,
                                    // orderQuantity: parseInt(text),
                                    insertQuantity: add_stock,
                                    // totalPrice: parseInt(text) * poItem.price,
                                  }
                                : poItem,
                            ),
                          );
                        }
                      }
                    }}
                    //////////////////////////////////////////////

                    onChangeText={text => {
                      if (text == null) {
                        setPoItems(
                          poItems.map((poItem, i) =>
                            i === index
                              ? {
                                  ...poItem,
                                  // orderQuantity: parseInt(text),
                                  insertQuantity: 0,
                                  // totalPrice: parseInt(text) * poItem.price,
                                }
                              : poItem,
                          ),
                        );
                      } else {
                        let add_stock = isNaN(parseInt(text))
                          ? 0
                          : parseInt(text);

                        setPoItems(
                          poItems.map((poItem, i) =>
                            i === index
                              ? {
                                  ...poItem,
                                  // orderQuantity: parseInt(text),
                                  insertQuantity: add_stock,
                                  // totalPrice: parseInt(text) * poItem.price,
                                }
                              : poItem,
                          ),
                        );
                      }
                    }}
                    defaultValue={poItems[index].insertQuantity}
                    // ref={myTextInput}
                  />
                </View>

                <TouchableOpacity
                  style={{marginLeft: 10}}
                  onPress={() => {
                    setPoItems([
                      ...poItems.slice(0, index),
                      ...poItems.slice(index + 1),
                    ]);
                  }}>
                  <Icon
                    name="trash-2"
                    size={switchMerchant ? 15 : 20}
                    color="#eb3446"
                  />
                </TouchableOpacity>
              </View>
            );
          })}
        </>
      );
    }
  };

  const renderOrderItem = ({item, index}) => {
    // console.log('renderOrderItem');
    // console.log(item);
    const itemNames = Object.values(item.items).map(poItem => poItem.n);
    const itemStocks = Object.values(item.items).map(poItem => poItem.sc);

    const combinedItems = itemNames.map(
      (name, idx) => `${name} (${itemStocks[idx]})`,
    );

    return (
      <TouchableOpacity
        onPress={() => {
          CommonStore.update(s => {
            s.selectedRackEdit = item;
          });

          setPurchaseOrder(false);
          setAddPurchase(true);

          /* CommonStore.update(s => {
                    s.selectedOutletCategoryEdit = item;
          
                    s.timestamp = Date.now;
                  }, () => {
                    navigation.navigate('Product');
                  }); */

          //    CommonStore.update((s) => {
          //        s.selectedOutletCategoryEdit = item;

          //        s.timestamp = Date.now;
          //    });

          //    setPurchaseOrder(false);
          //    setAddPurchase(true);
          //     setEditCategory(true);
        }}>
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
          }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              width: '30%',
              color: Colors.primaryColor,
            }}>
            {item.rackName}
          </Text>
          {/* <Text
                        style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            width: '60%',
                            color: Colors.primaryColor,
                        }}>
                        {`${itemNames ? `${itemNames.join('\n')} (${itemStocks.join('\n')})` : ''}`}
                    </Text> */}
          <View style={[styles.textStyle, {width: '50%'}]}>
            {combinedItems.map((combinedItem, idx) => (
              <Text
                key={idx}
                style={{
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.primaryColor,
                }}>
                {combinedItem}
              </Text>
            ))}
          </View>
          <View style={[styles.textStyle, {width: '20%'}]}>
            <TouchableOpacity
              style={{
                justifyContent: 'center',
                flexDirection: 'row',
                borderWidth: 1,
                borderColor: Colors.primaryColor,
                backgroundColor: '#0F1A3C',
                borderRadius: 5,
                //width: 160,
                paddingHorizontal: 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                //zIndex: -1,
              }}
              onPress={() => {
                setShowQRModal(true);
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                {/* <Icon name="upload" size={20} color={Colors.primaryColor} /> */}

                <Ionicons
                  name={'qr-code-outline'}
                  size={switchMerchant ? 10 : 20}
                  color={Colors.whiteColor}
                />
                <Text
                  style={{
                    color: Colors.whiteColor,
                    marginLeft: 5,
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                  QR
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          {/* <Text
                        style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            width: '20.3%',
                            color: Colors.primaryColor,
                        }}>

                        {/* {outletItems.filter(outletItem => outletItem.categoryId === item.uniqueId).length} *
                    </Text>
                    <Text
                        style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            width: '18%',
                        }}>

                    </Text> */}
        </View>
      </TouchableOpacity>
    );
  };

  const renderStockItem = ({item}) => {
    return (
      <TouchableOpacity
        onPress={() => {
          expandOrderFunc(item);
          setClickedProduct(item);
        }}>
        <View
          style={{
            backgroundColor:
              (inputStockIdealDict[item.uniqueId]
                ? parseFloat(inputStockIdealDict[item.uniqueId]) || 0
                : item.stockIdealQuantity || 0) <
              (inputStockWarningDict[item.uniqueId]
                ? parseFloat(inputStockWarningDict[item.uniqueId]) || 0
                : item.stockWarningQuantity || 0)
                ? '#D0342C'
                : '#FFFFFF',
            //#ed5740
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 10,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#C4C4C4',
            alignItems: 'center',
          }}>
          <Text
            style={{
              width: '22.5%',
              color:
                (inputStockIdealDict[item.uniqueId]
                  ? parseFloat(inputStockIdealDict[item.uniqueId]) || 0
                  : item.stockIdealQuantity || 0) <
                (inputStockWarningDict[item.uniqueId]
                  ? parseFloat(inputStockWarningDict[item.uniqueId]) || 0
                  : item.stockWarningQuantity || 0)
                  ? Colors.lightPrimary
                  : Colors.primaryColor,
              marginRight: 2,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {item.name}
          </Text>
          <Text
            style={{
              width: '12%',
              marginRight: 2,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {item.skuMerchant || '-'}
          </Text>
          <Text
            style={{
              width: '8%',
              marginRight: 2,
              color:
                item.quantity < item.stockIdealQuantity &&
                item.quantity > item.stockWarningQuantity
                  ? Colors.tabGold
                  : item.quantity < item.stockWarningQuantity
                  ? Colors.tabRed
                  : Colors.blackColor,
              fontWeight:
                item.quantity < item.stockIdealQuantity ? '500' : null,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
            }}>
            {(item.stockCount || 0).toFixed(0)}
          </Text>

          <View
            style={{
              width: '12%',
              marginRight: 2,
              //alignItems: 'center',
            }}>
            <TextInput
              editable
              //editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={[
                styles.textInput2,
                {
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                  width: switchMerchant ? 60 : 70,
                },
              ]}
              placeholder={(item.stockIdealQuantity || 0).toFixed(0)}
              placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
              //iOS
              clearTextOnFocus
              //////////////////////////////////////////////
              //Android
              onFocus={() => {
                setTemp(item.stockIdealQuantity);
                setInputStockIdealDict({
                  ...inputStockIdealDict,
                  [item.uniqueId]: '',
                });
              }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              onEndEditing={() => {
                if (item.stockIdealQuantity === '') {
                  if (temp !== '') {
                    setInputStockIdealDict({
                      ...inputStockIdealDict,
                      [item.uniqueId]: temp,
                    });
                  } else {
                    setInputStockIdealDict({
                      ...inputStockIdealDict,
                      [item.uniqueId]: 0,
                    });
                  }
                }
              }}
              //////////////////////////////////////////////
              onChangeText={text => {
                // setState({ ideal: text.trim(), itemId: result.itemInventory.itemId });
                setInputStockIdealDict({
                  ...inputStockIdealDict,
                  [item.uniqueId]: parseValidPriceText(text),
                });
              }}
              value={
                inputStockIdealDict[item.uniqueId]
                  ? inputStockIdealDict[item.uniqueId]
                  : //(item.stockIdealQuantity && !isNaN(item.stockIdealQuantity) ?
                    (item.stockIdealQuantity
                      ? item.stockIdealQuantity
                      : 0
                    ).toFixed(0)
                //.toFixed(0)
                //: '0')
              }
              keyboardType={'decimal-pad'}
            />
          </View>

          <View
            style={{
              width: '12%',
              marginRight: switchMerchant ? 5 : 2,
              //alignItems: 'center',
              //marginLeft: 30,
            }}>
            <TextInput
              editable
              // editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={[
                styles.textInput2,
                {
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                  width: switchMerchant ? 60 : 70,
                },
              ]}
              placeholder={(item.stockWarningQuantity || 0).toFixed(0)}
              placeholderTextColor={Platform.select({ios: '#a9a9a9'})}
              //iOS
              clearTextOnFocus
              //////////////////////////////////////////////
              //Android
              onFocus={() => {
                setTemp(item.stockWarningQuantity);
                setInputStockWarningDict({
                  ...inputStockWarningDict,
                  [item.uniqueId]: '',
                });
              }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              onEndEditing={() => {
                if (item.stockWarningQuantity === '') {
                  if (temp !== '') {
                    setInputStockWarningDict({
                      ...inputStockWarningDict,
                      [item.uniqueId]: temp,
                    });
                  } else {
                    setInputStockWarningDict({
                      ...inputStockWarningDict,
                      [item.uniqueId]: '',
                    });
                  }
                }
              }}
              onChangeText={text => {
                //setTemp(text)
                // setState({ ideal: text.trim(), itemId: result.itemInventory.itemId });
                setInputStockWarningDict({
                  ...inputStockWarningDict,
                  [item.uniqueId]: parseValidPriceText(text),
                });
              }}
              value={
                inputStockWarningDict[item.uniqueId]
                  ? inputStockWarningDict[item.uniqueId]
                  : //(item.stockWarningQuantity && !isNaN(item.stockWarningQuantity) ?
                    (item.stockWarningQuantity
                      ? item.stockWarningQuantity
                      : 0
                    ).toFixed(0)
                //.toFixed(0) : '0')
                // item.stockWarningQuantity.toFixed(0)
              }
              // defaultValue={
              //   inputStockWarningDict[item.uniqueId]
              //     ? inputStockWarningDict[item.uniqueId]
              //     : //(item.stockWarningQuantity && !isNaN(item.stockWarningQuantity) ?
              //       item.stockWarningQuantity
              //   //.toFixed(0) : '0')
              //   // item.stockWarningQuantity.toFixed(0)
              // }
              keyboardType={'decimal-pad'}
            />
          </View>

          <Text
            style={{
              width: '10.5%',
              marginRight: 2,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Regular',
              //marginLeft: 40
            }}>
            {' '}
            {skuOrderQuantityDict[item.sku]
              ? skuOrderQuantityDict[item.sku]
              : 0}
          </Text>

          <View style={{width: '18%', marginRight: 2}}>
            <TouchableOpacity
              onPress={() => {
                if (suppliersProductDict[item.supplierId || '']) {
                  CommonStore.update(
                    s => {
                      s.selectedSupplierEdit = suppliersProductDict[
                        item.supplierId || ''
                      ]
                        ? suppliersProductDict[item.supplierId || '']
                        : null;

                      s.routeParams = {
                        pageFrom: 'Inventory',
                      };
                    },
                    () => {
                      navigation.navigate('Supplier');
                    },
                  );
                }
              }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                {item.supplierId &&
                suppliersProductDict[item.supplierId] &&
                suppliersProductDict[item.supplierId].name
                  ? suppliersProductDict[item.supplierId].name
                  : 'N/A'}
              </Text>
            </TouchableOpacity>
          </View>

          <View
            style={{
              position: 'absolute',
              top: Platform.OS == 'ios' ? 3 : 23,
              right: Platform.OS == 'ios' ? 3 : 10,
              //top: Platform.OS == 'android' ? 3 : 23, right: Platform.OS == 'android' ? 3 : 10,
              alignItems: 'center',
            }}>
            {/* <TouchableOpacity onPress={() => { expandOrderFunc(item) }}> */}
            <Icon
              name={
                expandViewDict[item.uniqueId] ? 'chevron-up' : 'chevron-down'
              }
              size={switchMerchant ? 10 : 30}
              color={Colors.tabGrey}
              style={{
                top: Platform.OS == 'ios' ? 0 : 0,
                right: Platform.OS == 'ios' ? 0 : -5,
              }}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderProductUsingInventory = ({item, index}) => {
    return (
      <View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 10,
          //paddingHorizontal: 20,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          alignItems: 'center',
        }}>
        <View>
          <Text style={{fontWeight: '500', fontSize: switchMerchant ? 10 : 14}}>
            {item.name}
          </Text>
        </View>
      </View>
    );
  };

  const renderProductUsingInventory1 = ({item, index}) => {
    return (
      <View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 10,
          paddingHorizontal: 10,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          alignItems: 'center',
        }}>
        {item.image ? (
          <AsyncImage
            source={{uri: item.image}}
            item={item}
            style={{
              width: switchMerchant ? 45 : 50,
              height: switchMerchant ? 45 : 50,
              borderWidth: 1,
              borderColor: '#E5E5E5',
              borderRadius: 5,
              marginRight: 10,
            }}
            hideLoading
          />
        ) : (
          <View
            style={{
              width: switchMerchant ? 45 : 50,
              height: switchMerchant ? 45 : 50,
              borderWidth: 1,
              borderColor: '#E5E5E5',
              borderRadius: 5,
              marginRight: 10,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Colors.fieldtBgColor,
            }}>
            <Image
              style={{width: 35, height: 35}}
              source={require('../assets/image/dineinGrey.png')}
            />
          </View>
        )}
        <Text
          style={{
            fontWeight: '500',
            width: '35%',
            marginRight: 8,
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {item.name}
        </Text>
        <Text
          style={{
            fontWeight: '500',
            width: '30%',
            marginRight: 8,
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {item.skuMerchant || 'N/A'}
        </Text>
        <Text
          style={{
            fontWeight: '500',
            width: '15%',
            marginRight: 8,
            fontSize: switchMerchant ? 10 : 14,
          }}>
          {outletCategoriesDict[item.categoryId]
            ? outletCategoriesDict[item.categoryId].name
            : 'N/A'}
        </Text>
      </View>
    );
  };

  const insertInventory = () => {
    var body = {
      // poId: poId,
      poItems,
      // supplierId: selectedSupplierId,
      // status: poStatus,
      // outletId: selectedTargetOutletId,
      outletId: currOutletId,

      // outletName: allOutlets.find(outlet => outlet.uniqueId === currOutletId).name,

      merchantId,
      // remarks: '',
    };

    // console.log(body);

    APILocal.insertInventoryProduct({body}).then(result => {
      // ApiClient.POST(API.insertInventoryProduct, body).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          'Inventory has been inserted',
          [
            {
              text: 'OK',
              onPress: () => {
                //props.navigation.goBack();
                setLowStockAlert(true);
              },
            },
          ],
          {cancelable: false},
        );
      } else {
        Alert.alert('Error', 'Failed to create inventory');
      }
    });
  };

  const settingLowStock = () => {
    const stockIdealList = Object.entries(inputStockIdealDict).map(
      ([key, value]) => ({
        key,
        value: !isNaN(parseFloat(value)) ? parseFloat(value) : 0,
      }),
    );
    const stockWarningList = Object.entries(inputStockWarningDict).map(
      ([key, value]) => ({
        key,
        value: !isNaN(parseFloat(value)) ? parseFloat(value) : 0,
      }),
    );

    CommonStore.update(s => {
      s.isLoading = true;
    });

    var body = {
      stockIdealList,
      stockWarningList,
    };

    if (
      outletItems.find(
        item =>
          (inputStockIdealDict[item.uniqueId]
            ? parseFloat(inputStockIdealDict[item.uniqueId])
            : item.stockIdealQuantity) <
          (inputStockWarningDict[item.uniqueId]
            ? parseFloat(inputStockWarningDict[item.uniqueId])
            : item.stockWarningQuantity),
      )
    ) {
      Alert.alert(
        'Ideal stock quantity must be higher than the warning stock quantity',
      );
      CommonStore.update(s => {
        s.isLoading = false;
      });
      return;
    }

    ApiClient.POST(API.updateStockIdealWarningProduct, body).then(result => {
      if (result && result.status === 'success') {
        setInputStockIdealDict({});
        setInputStockWarningDict({});

        Alert.alert(
          'Success',
          'Ideal and warning stock has been updated',
          [{text: 'OK', onPress: () => {}}],
          {cancelable: false},
        );
      } else {
        Alert.alert(
          'Error',
          'Failed to update the ideal and warning stock',
          [{text: 'OK', onPress: () => {}}],
          {cancelable: false},
        );
      }

      CommonStore.update(s => {
        s.isLoading = false;
      });
    });
  };

  const createOutletItemCategory = async () => {
    // console.log('on createOutletItemCategory');

    if (rackName !== '') {
      if (selectedRackEdit === null) {
        var body = {
          rackName,
          outletId: currOutletId,
          merchantId,

          hideInOrderTypes,

          // isActive: effectiveDays == selectedEffectiveTypeOptions ? true : false,
          isActive: true,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          printKDNum,

          hideOutletSectionIdList: selectedHideOutletSectionIdList
            ? selectedHideOutletSectionIdList
            : [],

          uid: userId,
        };

        // console.log(body);

        //ApiClient.POST(API.createOutletItemCategory, body).then((result) => {
        APILocal.createOutletItemCategory(body).then(result => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Rack has been created',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    //isActiv    // props.navigation.goBack()
                    setEditPurchase(false);
                    setAddPurchase(false);
                    setPurchaseOrder(true);
                  },
                },
              ],
              {cancelable: false},
            );
          } else {
            Alert.alert(
              'Error',
              'Unable to create the rack, please try again.',
            );
          }
        });
      } else {
        var body = {
          rackName,
          outletId: currOutletId,
          hideInOrderTypes,

          uniqueId: selectedRackEdit.uniqueId,

          // isActive: isActive !== undefined ? isActive : true,
          isActive: true,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          hideOutletSectionIdList: selectedHideOutletSectionIdList
            ? selectedHideOutletSectionIdList
            : [],

          uid: userId,
        };

        // console.log(body);

        //ApiClient.POST(API.updateOutletItemCategory, body).then((result) => {
        APILocal.updateOutletItemCategory(body).then(result => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Rack has been updated',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // props.navigation.goBack()
                    setEditPurchase(false);
                    setAddPurchase(false);
                    setPurchaseOrder(true);
                  },
                },
              ],
              {cancelable: false},
            );
          } else {
            Alert.alert('Error', 'Failed to update Rack');
          }
        });
      }
    } else {
      Alert.alert('Error', 'Please fill in all information:\nCategory Name');
    }
  };

  const deleteOutletItemCategory = () => {
    var body = {
      outletItem: outletItemsUnsorted.filter(
        item => item.categoryId === categoryUniqueId,
      ),
      allOutletsItemAddOnDict,
      allOutletsItemAddOnChoiceDict,

      rackName,
      outletId: currOutletId,
      hideInOrderTypes,

      uniqueId: selectedRackEdit.uniqueId,
    };

    console.log('deleting body', body);

    APILocal.deleteOutletItemCategory({
      body,
      uid: userId,
    }).then(result => {
      if (result && result.status === 'success') {
        setTimeout(() => {
          Alert.alert(
            'Success',
            'Product category has been removed',
            [
              {
                text: 'OK',
                onPress: () => {
                  // props.navigation.goBack()
                  // setEditPurchase(false);
                  // setAddPurchase(false);
                  // setPurchaseOrder(true);
                },
              },
            ],
            {cancelable: false},
          );
        }, 500);

        setDeleteModal(false);
        setPurchaseOrder(true);
        setEditPurchase(false);
        setAddPurchase(false);
      } else {
        Alert.alert('Error', 'Failed to remove Product category');
      }
    });
  };

  const deleteSelected = () => {
    for (var i = 0; i < deleteList.length; i++) {
      console.log('IN LOOP', i);

      var body = {
        outletItem: outletItemsUnsorted.filter(
          item => item.categoryId === deleteList[i].uniqueId,
        ),
        allOutletsItemAddOnDict,
        allOutletsItemAddOnChoiceDict,

        rackName: deleteList[i].name,
        outletId: deleteList[i].outletId,
        hideInOrderTypes: deleteList[i].hideInOrderTypes,

        uniqueId: deleteList[i].uniqueId,
      };

      console.log('deleting body', body);

      CommonStore.update(s => {
        s.isLoading = true;
      });

      // ApiClient.POST(API.deleteOutletItemCategory, body)
      APILocal.deleteOutletItemCategory({
        body,
        uid: userId,
      });
    }

    setTimeout(() => {
      Alert.alert(
        'Success',
        `${deleteList.length} ${
          deleteList.length > 1 ? 'categories' : 'category'
        } have been deleted`,
      );
    }, 1000);

    CommonStore.update(s => {
      s.isLoading = false;
    });

    setDeleteList([]);
    setDeleteOn(false);
  };

  //////////////////////////////////////////////////////////

  // 2024-05-24 - rack changes

  const saveRack = async () => {
    // console.log('on createOutletItemCategory');
    const itemSkuList = poItems.map(item => item.sku);
    if (rackName !== '') {
      if (selectedRackEdit === null) {
        var body = {
          rackName,
          outletId: currOutletId,
          merchantId,
          outletName: currOutlet.name,
          merchantName,

          items: {},
          itemSkuList,

          // uid: userId,
        };

        poItems.forEach(item => {
          body.items[item.sku] = {
            id: item.supplyItemId,
            n: item.name,
            sc: item.quantity || 0,
            sku: item.sku,
            skuM: item.skuMerchant || '',
            bc: item.bc,
            u: Date.now(),
          };
        });

        // console.log(body);

        //ApiClient.POST(API.createOutletItemCategory, body).then((result) => {
        APILocal.createRack(body).then(result => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Rack has been created',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    //isActiv    // props.navigation.goBack()
                    setEditPurchase(false);
                    setAddPurchase(false);
                    setPurchaseOrder(true);
                  },
                },
              ],
              {cancelable: false},
            );
          } else {
            Alert.alert(
              'Error',
              'Unable to create the rack, please try again.',
            );
          }
        });
      } else {
        var body = {
          rackName,
          outletId: currOutletId,
          merchantId,
          outletName: currOutlet.name,
          merchantName,

          items: {},
          itemSkuList,

          rackId: selectedRackEdit.uniqueId,

          // uid: userId,
        };
        poItems.forEach(item => {
          body.items[item.sku] = {
            id: item.supplyItemId,
            n: item.name,
            sc: item.quantity || 0,
            sku: item.sku,
            skuM: item.skuMerchant || '',
            bc: item.bc,
            u: Date.now(),
          };
        });

        // console.log(body);

        //ApiClient.POST(API.updateOutletItemCategory, body).then((result) => {
        APILocal.updateRack(body).then(result => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Rack has been updated',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // props.navigation.goBack()
                    setEditPurchase(false);
                    setAddPurchase(false);
                    setPurchaseOrder(true);
                  },
                },
              ],
              {cancelable: false},
            );
          } else {
            Alert.alert('Error', 'Failed to update the rack');
          }
        });
      }
    } else {
      Alert.alert('Error', 'Please fill in all information:\nRack Name');
    }
  };

  const deleteRack = () => {
    var body = {
      rackId: selectedRackEdit.uniqueId,
    };

    console.log('deleting body', body);

    APILocal.deleteRack({
      body,
      uid: userId,
    }).then(result => {
      if (result && result.status === 'success') {
        setTimeout(() => {
          Alert.alert(
            'Success',
            'Rack has been removed',
            [
              {
                text: 'OK',
                onPress: () => {
                  // props.navigation.goBack()
                  // setEditPurchase(false);
                  // setAddPurchase(false);
                  // setPurchaseOrder(true);
                },
              },
            ],
            {cancelable: false},
          );
        }, 500);

        setDeleteModal(false);
        setPurchaseOrder(true);
        setEditPurchase(false);
        setAddPurchase(false);
      } else {
        Alert.alert('Error', 'Failed to remove the rack');
      }
    });
  };

  //////////////////////////////////////////////////////////

  // function end

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
                transform: [{scaleX: 1}, {scaleY: 1}],
              }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          },
        ]}>
        {/* <View
                    style={[
                        styles.sidebar,
                        !isTablet()
                            ? {
                                width: Dimensions.get('screen').width * 0.08,
                            }
                            : {},
                        ,
                        switchMerchant
                            ? {
                                // width: '10%'
                            }
                            : {},
                        {
                            width: windowWidth * 0.08,
                        }
                    ]}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={3}
                        expandInventory
                    />
                </View> */}
        <ScrollView scrollEnabled={switchMerchant} horizontal>
          <ScrollView scrollEnabled={switchMerchant}>
            <View
              style={[
                styles.content,
                {
                  padding: 16,
                  width:
                    Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
                },
              ]}>
              {purchaseOrder ? (
                <View
                  style={{
                    flexDirection: 'row',
                    width: Dimensions.get('screen').width * 0.87,
                    alignSelf: 'center',
                    justifyContent: 'space-between',
                    zIndex: 100,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingTop: 5,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 20 : 26,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      {/* {orderList.length} */}
                      {racks.length}
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 20 : 26,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: windowWidth * 0.008,
                      }}>
                      {racks.length > 1 ? 'Racks' : 'Rack'}
                    </Text>
                  </View>

                  <View
                    style={{
                      alignItems: 'flex-end',
                      flexDirection: 'row',
                      // paddingTop: '0.5%',
                    }}>
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#0F1A3C',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        //zIndex: -1,

                        marginLeft: 15,
                      }}
                      onPress={() => {
                        CommonStore.update(s => {
                          s.selectedRackEdit = null;
                        });
                        setPoItems([
                          {
                            supplyItemId: outletItems[0].uniqueId,
                            name: outletItems[0].name,
                            sku: outletItems[0].sku,
                            skuMerchant: outletItems[0].skuMerchant,
                            // quantity: outletSupplyItemsSkuDict[supplyItems[0].sku]
                            //   ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity
                            //   : 0, // check if the supply item sku for this outlet existed
                            quantity: outletItems[0].stockCount || 0,
                            unit: '',
                            // orderQuantity: 0,
                            // receivedQuantity: 0,
                            // price: supplyItems[0].price,
                            // totalPrice: 0,
                            insertQuantity: 0,
                            supplyItem: outletItems[0],
                            bc: outletItems[0].bc ? outletItems[0].bc : '',
                          },
                        ]);
                        setPurchaseOrder(false);
                        setAddPurchase(true);
                      }}>
                      <View
                        style={{flexDirection: 'row', alignItems: 'center'}}>
                        {/* <Icon name="upload" size={20} color={Colors.primaryColor} /> */}
                        <AntDesign
                          name="pluscircle"
                          size={switchMerchant ? 10 : 20}
                          color={Colors.whiteColor}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          RACK
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : null}

              {purchaseOrder ? (
                <View
                  style={{
                    width: Dimensions.get('screen').width * 0.87,
                    alignSelf: 'center',
                    marginTop: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      //alignItems: 'flex-end',
                      alignSelf: 'flex-end',
                      //justifyContent:'flex-end',
                      zIndex: -1,
                    }}>
                    {/* {changeLayout ?
                                            <View style={{ marginRight: 10, marginTop: 10 }}>
                                                <Tooltip
                                                    isVisible={sortingHelp}
                                                    content={<Text style={{
                                                        //fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Regular',
                                                    }}>
                                                        Press and hold to drag a Category
                                                    </Text>
                                                    }
                                                    placement="top"
                                                    onClose={() => setSortingHelp(false)}
                                                >
                                                    <TouchableOpacity
                                                        onPress={() => setSortingHelp(true)}
                                                        style={styles.touchable}>
                                                        <Icon
                                                            name="help-circle"
                                                            size={switchMerchant ? 10 : 20}
                                                            style={{ color: Colors.primaryColor }}
                                                        />
                                                    </TouchableOpacity>
                                                </Tooltip>
                                            </View>
                                            : <></>}
                                        {deleteOn ?
                                            <View style={{ marginRight: 10, marginTop: 10 }}>
                                                <Tooltip
                                                    isVisible={deleteHelp}
                                                    content={
                                                        <Text style={{
                                                            //fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Regular',
                                                        }}>
                                                            Select a Category to delete
                                                        </Text>
                                                    }
                                                    placement="top"
                                                    onClose={() => setDeleteHelp(false)}
                                                >
                                                    <TouchableOpacity
                                                        onPress={() => setDeleteHelp(true)}
                                                        style={styles.touchable}>
                                                        <Icon
                                                            name="help-circle"
                                                            size={switchMerchant ? 10 : 20}
                                                            style={{ color: Colors.primaryColor }}
                                                        />
                                                    </TouchableOpacity>
                                                </Tooltip>
                                            </View>
                                            : <></>}
                                        {deleteOn || changeLayout ?
                                            <TouchableOpacity
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.fieldtBgColor,
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    borderRadius: 5,
                                                    //width: 160,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,

                                                    marginRight: 10,
                                                }}
                                                onPress={() => {
                                                    if (deleteOn) {
                                                        setDeleteOn(false);
                                                        setDeleteList([]);
                                                    }
                                                    else {
                                                        setChangeLayout(false);
                                                    }
                                                }}>
                                                <View
                                                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                    {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> *
                                                    <Text
                                                        style={{
                                                            color: Colors.descriptionColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        CANCEL
                                                    </Text>
                                                </View>
                                            </TouchableOpacity>
                                            : <></>} */}
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: deleteOn ? 'grey' : Colors.primaryColor,
                        backgroundColor: deleteOn ? 'grey' : '#0F1A3C',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        marginRight: 10,
                      }}
                      disabled={deleteOn}
                      onPress={() => {
                        // setDeleteOn(!deleteOn);
                        // if (changeLayout) {
                        //     updateOutletCategoryOrderIndex();
                        // }
                      }}>
                      <View
                        style={{flexDirection: 'row', alignItems: 'center'}}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          <MaterialIcons
                            name="print"
                            size={20}
                            color={Colors.whiteColor}
                          />
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <View
                      style={{
                        width: switchMerchant ? 200 : 250,
                        height: switchMerchant ? 35 : 40,
                        backgroundColor: 'white',
                        borderRadius: 5,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',
                        alignSelf: 'flex-end',

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      }}>
                      <Icon
                        name="search"
                        size={switchMerchant ? 13 : 18}
                        color={Colors.primaryColor}
                        style={{marginLeft: 15}}
                      />
                      <TextInput
                        editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={{
                          width: switchMerchant ? 180 : 220,
                          fontSize: switchMerchant ? 10 : 15,
                          fontFamily: 'NunitoSans-Regular',
                          paddingLeft: 5,
                          height: 45,
                        }}
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={text => {
                          // setSearch(text.trim());
                          setSearch(text);
                        }}
                        value={search}
                      />
                    </View>
                  </View>

                  <View style={{}}>
                    <View
                      style={{
                        backgroundColor: Colors.whiteColor,
                        width: Dimensions.get('screen').width * 0.87,
                        // height: Dimensions.get('screen').height * 0.68,
                        height: Dimensions.get('screen').height * 0.6,
                        marginTop: 20,
                        marginHorizontal: 30,
                        marginBottom: 30,
                        alignSelf: 'center',
                        borderRadius: 5,
                        shadowOpacity: 0,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        //borderRadius: 10,
                      }}>
                      <View
                        style={{
                          backgroundColor: Colors.whiteColor,
                          flexDirection: 'row',
                          paddingVertical: 20,
                          paddingHorizontal: 20,
                          marginTop: switchMerchant ? -20 : -10,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                          borderRadius: 5,
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            // width: '40%',
                            width: '30%',
                            alignSelf: 'center',
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Rack Name
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            // width: '60%',
                            width: '50%',
                            alignSelf: 'center',
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Items / Quantity
                        </Text>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            // width: '60%',
                            width: '20%',
                            alignSelf: 'center',
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          Action
                        </Text>
                        {/* <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    width: '21%',
                                                    alignSelf: 'center',
                                                    color: Colors.blackColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                Rack Group
                                            </Text> */}
                        {/* <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    width: '20%',
                                                    alignSelf: 'center',
                                                    color: Colors.blackColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                Sort Order
                                            </Text> */}
                        {/* <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    width: '19%',
                                                    alignSelf: 'center',
                                                    color: Colors.blackColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                Note
                                            </Text> */}
                      </View>

                      {changeLayout ? (
                        <View style={{paddingBottom: 55}}>
                          <NestableScrollContainer>
                            <NestableDraggableFlatList
                              // data={filteredOutletItems.map(category => {
                              //     var totalItemQuantity = 0;

                              //     for (var i = 0; i < outletItems.length; i++) {
                              //         //// console.log('hello')
                              //         if (outletItems[i].categoryId === category.uniqueId) {
                              //             //// console.log('123hihi')
                              //             totalItemQuantity += 1;
                              //         }
                              //     }

                              //     return {
                              //         ...category,
                              //         totalItemQuantity,
                              //     };
                              // }).filter((item) => {
                              //     // var totalQuantity = 0;

                              //     // for (var i = 0; i < outletItems.length; i++) {
                              //     //   //// console.log('hello')
                              //     //   if (outletItems[i].categoryId === item.uniqueId) {
                              //     //     //// console.log('123hihi')
                              //     //     totalQuantity += 1;
                              //     //   }
                              //     // }

                              //     if (search !== '') {
                              //         const searchLowerCase = search.toLowerCase();

                              //         if (
                              //             item.name.toLowerCase().includes(searchLowerCase)
                              //         ) {
                              //             return true;
                              //         } else if (
                              //             item.totalItemQuantity.toString().includes(searchLowerCase)
                              //         ) {
                              //             return true;
                              //         } else {
                              //             return false;
                              //         }
                              //     } else {
                              //         return true;
                              //     }
                              // })}
                              data={racks}
                              /* extraData={outletCategories.filter(item => {
                                                              if (search !== '') {
                                                                return item.name.toLowerCase().includes(search.toLowerCase());
                                                              }
                                                              else {
                                                                return true;
                                                              }
                                                            })} */
                              nestedScrollEnabled
                              renderItem={renderOrderItemSort}
                              keyExtractor={(item, index) =>
                                `draggable-category-${item.uniqueId}`
                              }
                              showsVerticalScrollIndicator={false}
                              //scrollEnabled={true}
                              //keyboardShouldPersistTaps="handled"
                              maxToRenderPerBatch={1}
                              // onDragBegin={() => {
                              //   setDraggingScroll(false);
                              //   setStartDragging(true);
                              // }}
                              onDragEnd={({data, from, to}) => {
                                setStartDragging(false);
                                setDraggingScroll(true);

                                // sortingTemp = {
                                //     ...sortedCategoryOrderIndexIdList,
                                // };

                                for (var i = 0; i < data.length; i++) {
                                  sortingTemp = {
                                    ...sortingTemp,
                                    [data[i].uniqueId]: i + 1,
                                  };
                                }

                                setIsSavingList(true);
                                // setSortedCategoryOrderIndexIdList(sortingTemp)
                              }}
                            />
                          </NestableScrollContainer>
                        </View>
                      ) : (
                        <ScrollView
                          scrollEnabled={!switchMerchant}
                          nestedScrollEnabled>
                          {deleteOn ? (
                            <FlashList
                              data={
                                racks
                                // filteredOutletItems
                                // sortedCategory.map(category => {
                                //   var totalItemQuantity = 0;

                                //   for (var i = 0; i < outletItems.length; i++) {
                                //     //// console.log('hello')
                                //     if (outletItems[i].categoryId === category.uniqueId) {
                                //       //// console.log('123hihi')
                                //       totalItemQuantity += 1;
                                //     }
                                //   }

                                //   return {
                                //     ...category,
                                //     totalItemQuantity,
                                //   };
                                // }).filter((item) => {
                                //   // var totalQuantity = 0;

                                //   // for (var i = 0; i < outletItems.length; i++) {
                                //   //   //// console.log('hello')
                                //   //   if (outletItems[i].categoryId === item.uniqueId) {
                                //   //     //// console.log('123hihi')
                                //   //     totalQuantity += 1;
                                //   //   }
                                //   // }

                                //   if (search !== '') {
                                //     const searchLowerCase = search.toLowerCase();

                                //     if (
                                //       item.name.toLowerCase().includes(searchLowerCase)
                                //     ) {
                                //       return true;
                                //     } else if (
                                //       item.totalItemQuantity.toString().includes(searchLowerCase)
                                //     ) {
                                //       return true;
                                //     } else {
                                //       return false;
                                //     }
                                //   } else {
                                //     return true;
                                //   }
                                // })
                              }
                              /* extraData={outletCategories.filter(item => {
                                                              if (search !== '') {
                                                                return item.name.toLowerCase().includes(search.toLowerCase());
                                                              }
                                                              else {
                                                                return true;
                                                              }
                                                            })} */
                              nestedScrollEnabled
                              renderItem={renderOrderItemDelete}
                              keyExtractor={(item, index) => String(index)}
                            />
                          ) : (
                            <FlashList
                              data={
                                racks
                                // filteredOutletItems
                                // sortedCategory.map(category => {
                                //   var totalItemQuantity = 0;

                                //   for (var i = 0; i < outletItems.length; i++) {
                                //     //// console.log('hello')
                                //     if (outletItems[i].categoryId === category.uniqueId) {
                                //       //// console.log('123hihi')
                                //       totalItemQuantity += 1;
                                //     }
                                //   }

                                //   return {
                                //     ...category,
                                //     totalItemQuantity,
                                //   };
                                // }).filter((item) => {
                                //   // var totalQuantity = 0;

                                //   // for (var i = 0; i < outletItems.length; i++) {
                                //   //   //// console.log('hello')
                                //   //   if (outletItems[i].categoryId === item.uniqueId) {
                                //   //     //// console.log('123hihi')
                                //   //     totalQuantity += 1;
                                //   //   }
                                //   // }

                                //   if (search !== '') {
                                //     const searchLowerCase = search.toLowerCase();

                                //     if (
                                //       item.name.toLowerCase().includes(searchLowerCase)
                                //     ) {
                                //       return true;
                                //     } else if (
                                //       item.totalItemQuantity.toString().includes(searchLowerCase)
                                //     ) {
                                //       return true;
                                //     } else {
                                //       return false;
                                //     }
                                //   } else {
                                //     return true;
                                //   }
                                // })
                              }
                              /* extraData={outletCategories.filter(item => {
                                                              if (search !== '') {
                                                                return item.name.toLowerCase().includes(search.toLowerCase());
                                                              }
                                                              else {
                                                                return true;
                                                              }
                                                            })} */
                              nestedScrollEnabled
                              renderItem={renderOrderItem}
                              keyExtractor={(item, index) => String(index)}
                            />
                          )}

                          <View style={{height: 100}} />
                        </ScrollView>
                      )}
                    </View>
                  </View>
                </View>
              ) : null}

              {addPurchase ? (
                <View
                  style={{
                    height: switchMerchant
                      ? windowHeight - 100
                      : windowHeight - 200,
                    width: switchMerchant
                      ? windowWidth * 0.86
                      : windowWidth * 0.91,
                  }}>
                  <View>
                    <Modal
                      supportedOrientations={['landscape', 'portrait']}
                      style={{flex: 1}}
                      visible={deleteModal}
                      transparent
                      animationType="slide">
                      <View style={styles.modalContainer}>
                        <View
                          style={[
                            styles.modalView1,
                            {
                              height: windowHeight * 0.25,

                              width: Dimensions.get('window').width * 0.4,
                              padding: Dimensions.get('window').width * 0.035,
                              paddingTop: Dimensions.get('window').width * 0.05,

                              ...getTransformForModalInsideNavigation(),
                            },
                          ]}>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                              setDeleteModal(false);
                            }}>
                            <AIcon
                              name="closecircle"
                              size={25}
                              color={Colors.fieldtTxtColor}
                            />
                          </TouchableOpacity>
                          <View style={{}}>
                            <Text
                              style={{
                                fontWeight: '700',
                                fontFamily: 'Nunitosans-Regular',
                                fontSize: 18,
                              }}>
                              {/* Please Key In The Authorization Code */}
                              {`Are you sure you want to remove this category ${
                                selectedRackEdit
                                  ? selectedRackEdit.rackName
                                  : ''
                              } ?`}
                            </Text>
                          </View>

                          <View style={{}}>
                            <TouchableOpacity
                              style={{
                                borderRadius: 5,
                                backgroundColor: Colors.tabRed,
                                height: 35,
                                width: 90,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onPress={() => {
                                // deleteOutletItemCategory();
                                deleteRack();
                              }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: 'white',
                                  fontWeight: '500',
                                }}>
                                DELETE
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </Modal>

                    <Modal
                      supportedOrientations={['landscape', 'portrait']}
                      style={{flex: 1}}
                      visible={hideModal}
                      transparent
                      animationType="slide">
                      <View style={styles.modalContainer}>
                        <View
                          style={[
                            styles.modalView1,
                            {
                              height: windowHeight * 0.6,
                              width: Dimensions.get('window').width * 0.5,

                              padding: Dimensions.get('window').width * 0.035,
                              paddingTop: Dimensions.get('window').width * 0.05,

                              ...getTransformForModalInsideNavigation(),
                            },
                          ]}>
                          <Text
                            style={{
                              fontWeight: '700',
                              fontFamily: 'Nunitosans-Bold',
                              fontSize: 30,
                            }}>
                            {`${
                              selectedRackEdit ? selectedRackEdit.rackName : ''
                            } is Out of Stock`}
                          </Text>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                              setHideModal(false);
                              setSelectedChoice(null);
                            }}>
                            <AIcon
                              name="closecircle"
                              size={25}
                              color={Colors.fieldtTxtColor}
                            />
                          </TouchableOpacity>
                          <View style={{}}>
                            <Text
                              style={{
                                //fontWeight: '700',
                                fontFamily: 'Nunitosans-Regular',
                                fontSize: 14,
                                color: 'gray',
                              }}>
                              <Text>Please confirm that </Text>
                              <Text style={{fontWeight: '700'}}>
                                {selectedRackEdit
                                  ? selectedRackEdit.rackName
                                  : ''}
                              </Text>
                              <Text> is out of stock</Text>
                            </Text>
                          </View>

                          <View
                            style={{
                              marginTop: 20,
                              justifyContent: 'flex-start',
                              width: '100%',
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={{
                                //fontWeight: '700',
                                fontFamily: 'Nunitosans-Regular',
                                fontSize: 16,
                              }}>
                              Select your preferred time:
                            </Text>
                            <View style={{marginLeft: 10, marginTop: 1}}>
                              <Tooltip
                                isVisible={hideHelp}
                                content={
                                  <Text>
                                    This category will be hidden from users
                                  </Text>
                                }
                                placement="top"
                                onClose={() => setHideHelp(false)}>
                                <TouchableOpacity
                                  onPress={() => setHideHelp(true)}
                                  style={styles.touchable}>
                                  <Icon
                                    name="help-circle"
                                    size={switchMerchant ? 10 : 20}
                                    style={{color: Colors.primaryColor}}
                                  />
                                </TouchableOpacity>
                              </Tooltip>
                            </View>
                          </View>

                          <View
                            style={{
                              marginTop: 20,
                            }}>
                            <RadioForm formHorizontal animation>
                              {/* To create radio buttons, loop through your array of options */}
                              {hideCategoryChoice.map((obj, i) => (
                                <RadioButton labelHorizontal key={i}>
                                  <RadioButtonInput
                                    obj={obj}
                                    //index={i}
                                    isSelected={selectedChoice === obj.value}
                                    onPress={item => {
                                      setSelectedChoice(obj.value);

                                      if (item === 'CUSTOM_TIME') {
                                        setCustomTimeSelectStart(true);
                                      }
                                    }}
                                    borderWidth={1}
                                    buttonInnerColor={Colors.primaryColor}
                                    buttonOuterColor={'#000'}
                                    buttonSize={14}
                                    buttonOuterSize={22}
                                    buttonStyle={{}}
                                    buttonWrapStyle={{
                                      marginLeft: Platform.OS == 'ios' ? 5 : 10,
                                    }}
                                    style={{marginTop: 20}}
                                  />
                                  <Text
                                    style={{
                                      fontSize: 14,
                                      color: 'black',
                                      fontFamily: 'Nunitosans-Regular',
                                      marginLeft: 5,
                                      marginTop: 1,
                                    }}>
                                    {obj.label}
                                  </Text>
                                </RadioButton>
                              ))}
                            </RadioForm>
                          </View>

                          {selectedChoice === 'CUSTOM_TIME' ? (
                            <View
                              style={{
                                marginTop: 5,
                              }}>
                              <Text
                                style={{
                                  color: 'gray',
                                  fontFamily: 'Nunitosans-Regular',
                                  fontSize: 14,
                                }}>
                                {`Selected Time: ${moment(
                                  customStartTime,
                                ).format('DD MMM YYYY hh:mm A')} - ${moment(
                                  customEndTime,
                                ).format('DD MMM YYYY hh:mm A')}`}
                              </Text>
                            </View>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'center',
                              marginTop: 20,
                            }}>
                            <TouchableOpacity
                              style={{
                                borderRadius: 5,
                                backgroundColor: Colors.primaryColor,
                                height: 35,
                                width: 90,
                                alignItems: 'center',
                                justifyContent: 'center',

                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              }}
                              onPress={() => {
                                if (selectedChoice == null) {
                                  Alert.alert(
                                    'Error',
                                    'Please select your preferred time to continue',
                                    [
                                      {
                                        text: 'OK',
                                      },
                                      {
                                        text: 'CANCEL',
                                        onPress: () => {
                                          setHideModal(false);
                                        },
                                      },
                                    ],
                                  );
                                } else {
                                  setIsHidden(true);
                                  setHideModal(false);
                                }
                              }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: 'white',
                                  fontWeight: '500',
                                }}>
                                CONFIRM
                              </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                              style={{
                                borderRadius: 5,
                                backgroundColor: Colors.fieldtBgColor,
                                height: 35,
                                width: 90,
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: 10,

                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              }}
                              onPress={() => {
                                setHideModal(false);
                                setSelectedChoice(null);
                              }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: 'grey',
                                  fontWeight: '500',
                                }}>
                                CANCEL
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </Modal>

                    <Modal
                      supportedOrientations={['landscape', 'portrait']}
                      style={{flex: 1}}
                      visible={visible}
                      transparent
                      animationType="slide">
                      <View
                        style={{
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                          minHeight: windowHeight,
                        }}>
                        <View style={styles.confirmBox}>
                          <TouchableOpacity
                            onPress={() => {
                              setState({visible: false});
                            }}>
                            <View
                              style={{
                                alignSelf: 'flex-end',
                                padding: 16,
                              }}>
                              {/* <Close name="closecircle" size={25} /> */}
                              <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                              />
                            </View>
                          </TouchableOpacity>
                          <View>
                            <Text
                              style={{
                                textAlign: 'center',
                                fontWeight: '700',
                                fontSize: 28,
                              }}>
                              Purchase Order
                            </Text>
                          </View>
                          <View style={{marginTop: 20}}>
                            <Text
                              style={{
                                textAlign: 'center',
                                color: Colors.descriptionColor,
                                fontSize: 25,
                              }}>
                              Fill In The Email Information
                            </Text>
                          </View>
                          <View
                            style={{
                              backgroundColor: 'white',
                              alignSelf: 'center',
                              flexDirection: 'row',
                            }}>
                            <Text style={{fontSize: 20, marginTop: 70}}>
                              Email:
                            </Text>
                            <View
                              style={{
                                marginTop: 60,
                                backgroundColor: '#f7f5f5',
                                marginLeft: 10,
                              }}>
                              <TextInput
                                editable={!loading}
                                underlineColorAndroid={Colors.fieldtBgColor}
                                clearButtonMode="while-editing"
                                style={styles.textCapacity}
                                placeholder="<EMAIL>"
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                onChangeText={text => {
                                  setState({Email: text});
                                }}
                                value={Email}
                              />
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignSelf: 'center',
                              marginTop: 20,
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '50%',
                              alignContent: 'center',
                              zIndex: 6000,
                            }}
                          />
                          <View
                            style={{
                              alignSelf: 'center',
                              marginTop: 20,
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: 250,
                              height: 40,
                              alignContent: 'center',
                              flexDirection: 'row',
                              marginTop: 40,
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                email(), setState({visible: false});
                              }}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: '60%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                alignContent: 'center',
                                borderRadius: 10,
                                height: 60,
                              }}>
                              <Text
                                style={{
                                  fontSize: 28,
                                  color: Colors.primaryColor,
                                }}>
                                Send
                              </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                setState({visible: false});
                              }}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: '60%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                alignContent: 'center',
                                borderRadius: 10,
                                height: 60,
                                marginLeft: 30,
                              }}>
                              <Text
                                style={{
                                  fontSize: 28,
                                  color: Colors.primaryColor,
                                }}>
                                No
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </Modal>

                    <TouchableOpacity
                      style={{
                        marginBottom: switchMerchant ? 0 : 0,
                        flexDirection: 'row',
                        alignContent: 'center',
                        alignItems: 'center',
                        marginTop: 5,
                        paddingLeft: switchMerchant
                          ? 0
                          : Platform.OS === 'ios'
                          ? '2%'
                          : '2%',
                      }}
                      onPress={() => {
                        setPurchaseOrder(true);
                        setAddPurchase(false);
                      }}>
                      <Icon
                        name="chevron-left"
                        size={switchMerchant ? 20 : 30}
                        color={Colors.primaryColor}
                      />
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 14 : 17,
                          color: Colors.primaryColor,
                          marginBottom: Platform.OS === 'ios' ? 0 : 2,
                          marginLeft: '-0.5%',
                        }}>
                        {' '}
                        Back{' '}
                      </Text>
                    </TouchableOpacity>

                    <ScrollView
                      nestedScrollEnabled
                      contentContainerStyle={{
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      style={
                        switchMerchant
                          ? {
                              // justifyContent: 'center',
                              // alignItems: 'center',
                              //width: windowWidth * 0.79,
                              backgroundColor: Colors.whiteColor,
                              borderRadius: 10,
                              backgroundColor: Colors.whiteColor,
                              minHeight: windowHeight * 0.1,
                              marginTop: switchMerchant ? 10 : 20,
                              marginHorizontal: 0,
                              marginBottom: 30,
                              //alignSelf: 'center',
                              borderRadius: 5,
                              shadowOpacity: 0,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                            }
                          : {
                              // justifyContent: 'center',
                              // alignItems: 'center',
                              backgroundColor: Colors.whiteColor,
                              borderRadius: 10,
                              backgroundColor: Colors.whiteColor,
                              //width: windowWidth * 0.89,
                              minHeight: windowHeight * 0.1,
                              marginTop: 30,
                              marginHorizontal: 30,
                              marginBottom: 30,
                              //alignSelf: 'center',
                              borderRadius: 5,
                              shadowOpacity: 0,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                            }
                      }>
                      {/* <View style={{ width: 140, marginRight: 10, marginTop: 10, alignSelf: "flex-end" }}>
                  <DropDownPicker
                    items={[
                      {
                        label: '🖨️  Print P.O',
                        value: 'Print P.O',
                      },
                      {
                        label: '📧  Email P.O',
                        value: 'Chicken',
                      },
                      {
                        label: '📤  Export Labels',
                        value: 'Export Labels',
                      },
                      {
                        label: '❌  Cancel P.O',
                        value: 'Cancel P.O',
                      },
                      {
                        label: '🗑️  Delete P.O',
                        value: 'Delete P.O',
                      },
                    ]}
                    defaultValue={choice2}
                    placeholder=""
                    containerStyle={{ height: 30 }}
                    style={{ backgroundColor: '#FAFAFA' }}
                    itemStyle={{
                      justifyContent: 'flex-start',
                    }}
                    dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                    onChangeItem={(item) =>
                      setState({
                        choice2: item.value,
                      })
                    }
                  />
                </View> */}
                      <View style={{}}>
                        <View
                          style={{
                            alignSelf: 'flex-end',
                            marginTop: switchMerchant ? 10 : 20,
                            // position: 'absolute',
                            zIndex: 10000,
                            right: 50,
                          }}>
                          {editCategory ? (
                            <>
                              <View
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.tabRed,
                                  backgroundColor: Colors.tabRed,
                                  borderRadius: 5,
                                  width: switchMerchant ? 100 : 130,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <TouchableOpacity
                                  style={{
                                    width: 130,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}
                                  onPress={() => {
                                    setDeleteModal(true);
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    DELETE
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#0F1A3C',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 130,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginTop: switchMerchant ? 10 : 10,
                            }}>
                            <TouchableOpacity
                              style={{
                                width: 130,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onPress={saveRack}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {editCategory ? 'SAVE' : 'ADD'}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View style={{marginTop: 10}}>
                          <Text
                            style={{
                              alignSelf: 'center',
                              marginTop: 30,
                              fontSize: switchMerchant ? 20 : 40,
                              fontWeight: 'bold',
                            }}>
                            {editCategory ? 'Edit Rack' : 'Add Rack'}
                          </Text>
                          <Text
                            style={{
                              alignSelf: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              color: '#adadad',
                            }}>
                            Fill In The Rack Information
                          </Text>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            marginTop: 10,
                            justifyContent: 'space-evenly',
                            marginTop: 60,
                            marginBottom: 40,
                            width: '90%',
                            alignSelf: 'center',
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              alignItems: 'center',
                              // justifyContent: 'space-around',
                              // width: '50%',
                            }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-SemiBold',
                                fontSize: switchMerchant ? 10 : 14,
                                width: 100,
                                textAlign: 'left',
                              }}>
                              Rack Name
                            </Text>

                            {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}

                            <TextInput
                              editable
                              underlineColorAndroid={Colors.fieldtBgColor}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 250,
                                height: switchMerchant ? 35 : 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              // placeholder={'50'}
                              placeholder={'Rack Name'}
                              placeholderStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              keyboardType={'default'}
                              onChangeText={text => {
                                setRackName(text);
                              }}
                              value={rackName}
                            />
                          </View>

                          {/* <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            flex: 1,
                                                            alignItems: 'center',
                                                            width: '50%',
                                                        }}>
                                                        <Text
                                                            style={{
                                                                fontFamily: 'NunitoSans-Regular',
                                                                fontSize: switchMerchant ? 10 : 14,
                                                                width: '40%',
                                                                textAlign: 'left',
                                                            }}>
                                                            Rack Group
                                                        </Text>

                                                        <View
                                                            style={{
                                                                width: '50%',
                                                                zIndex: 1000,
                                                            }}>
                                                            <DropDownPicker
                                                                containerStyle={{
                                                                    height: switchMerchant ? 35 : 40,
                                                                    zIndex: 2,
                                                                }}
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={{ fontWeight: 'bold' }}
                                                                labelStyle={{
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}
                                                                style={{
                                                                    width: switchMerchant ? 130 : 230,
                                                                    paddingVertical: 0,
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    borderRadius: 10,
                                                                }}
                                                                placeholderStyle={{ color: Colors.fieldtTxtColor }}
                                                                itemStyle={{
                                                                    justifyContent: 'flex-start',
                                                                    marginLeft: 5,
                                                                    zIndex: 2,
                                                                }}
                                                                items={[
                                                                    {
                                                                        label: 'Dine In',
                                                                        value: ORDER_TYPE.DINEIN,
                                                                    },
                                                                    {
                                                                        label: 'Takeaway',
                                                                        value: ORDER_TYPE.PICKUP,
                                                                    },
                                                                    {
                                                                        label: 'Delivery',
                                                                        value: ORDER_TYPE.DELIVERY,
                                                                    },
                                                                ]}
                                                                placeholder="Order Type"
                                                                multipleText={'%d type(s) selected'}
                                                                customTickIcon={(press) => (
                                                                    <Ionicons
                                                                        name={'md-checkbox'}
                                                                        color={
                                                                            press
                                                                                ? Colors.fieldtBgColor
                                                                                : Colors.primaryColor
                                                                        }
                                                                        size={25}
                                                                    />
                                                                )}
                                                                onChangeItem={(items) => {
                                                                    setHideInOrderTypes(items);
                                                                }}
                                                                defaultValue={hideInOrderTypes}
                                                                multiple
                                                                dropDownMaxHeight={150}
                                                                dropDownStyle={{
                                                                    width: switchMerchant ? 130 : 230,
                                                                    height: 90,
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    borderRadius: 10,
                                                                    borderWidth: 1,
                                                                    textAlign: 'left',
                                                                    zIndex: 2,
                                                                    fontSize: switchMerchant ? 11 : 14,
                                                                }}
                                                            />
                                                        </View>
                                                    </View> */}
                        </View>

                        {/* <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        //marginTop: 10,
                                                        justifyContent: 'space-evenly',
                                                        marginBottom: 40,
                                                        width: '90%',
                                                        alignSelf: 'center',
                                                        marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                                                        zIndex: -10,
                                                    }}>
                                                    <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center' }}>
                                                        <Text
                                                            style={{
                                                                fontFamily: 'NunitoSans-Regular',
                                                                fontSize: switchMerchant ? 10 : 14,
                                                                width: '20%',
                                                                textAlign: 'left',
                                                            }}>
                                                            Sort Order
                                                        </Text>
                                                        <View
                                                            style={[
                                                                styles.textInput4,
                                                                {
                                                                    width: switchMerchant
                                                                        ? windowWidth * 0.1
                                                                        : windowWidth * 0.15,
                                                                    height: switchMerchant ? 35 : 40,
                                                                    width: '30%',
                                                                },
                                                            ]}>
                                                            <DropDownPicker
                                                                containerStyle={{
                                                                    height: switchMerchant ? 35 : 40,
                                                                    zIndex: 2,
                                                                }}
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={{ fontWeight: 'bold' }}
                                                                labelStyle={{
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                    color: !isAvailableDayActive
                                                                        ? Colors.descriptionColor
                                                                        : 'black',
                                                                }}
                                                                style={{
                                                                    width: switchMerchant
                                                                        ? windowWidth * 0.1
                                                                        : windowWidth * 0.15,
                                                                    paddingVertical: 0,
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    borderRadius: 10,
                                                                    fontSize: switchMerchant ? 11 : 14,
                                                                }}
                                                                placeholderStyle={{
                                                                    color: Colors.fieldtTxtColor,
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14,
                                                                }}
                                                                items={EFFECTIVE_DAY_DROPDOWN_LIST1}
                                                                itemStyle={{
                                                                    justifyContent: 'flex-start',
                                                                    marginLeft: 5,
                                                                    zIndex: 3,
                                                                }}
                                                                placeholder={'Monday'}
                                                                multiple
                                                                multipleText={'%d day(s) selected'}
                                                                customTickIcon={(press) => (
                                                                    <Ionicon
                                                                        name={'md-checkbox'}
                                                                        color={
                                                                            !isAvailableDayActive
                                                                                ? Colors.descriptionColor
                                                                                : press
                                                                                    ? Colors.fieldtBgColor
                                                                                    : Colors.primaryColor
                                                                        }
                                                                        size={25}
                                                                    />
                                                                )}
                                                                onChangeItem={(items) => {
                                                                    setSelectedEffectiveTypeOptions(items);
                                                                }}
                                                                defaultValue={selectedEffectiveTypeOptions}
                                                                dropDownMaxHeight={150}
                                                                dropDownStyle={{
                                                                    width: switchMerchant
                                                                        ? windowWidth * 0.1
                                                                        : windowWidth * 0.15,
                                                                    height: 90,
                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                    borderRadius: 10,
                                                                    borderWidth: 1,
                                                                    textAlign: 'left',
                                                                    zIndex: 3,
                                                                }}
                                                                globalTextStyle={{
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}

                                                            />
                                                        </View>
                                                    </View>
                                                </View> */}

                        {/* ////////////////////////////// */}
                        {/* 2023-3-2 - Tag */}
                        {/* <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        // justifyContent: 'space-evenly',
                                                        marginTop: 20,
                                                        marginBottom: 80,
                                                        width: '90%',
                                                        alignSelf: 'center',
                                                        alignItems: 'center',
                                                        marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                                                    }}>
                                                    <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center', zIndex: -11, }}>
                                                        <Text
                                                            style={{
                                                                fontFamily: 'NunitoSans-Regular',
                                                                fontSize: switchMerchant ? 10 : 14,
                                                                width: '20%',
                                                                textAlign: 'left',
                                                            }}>
                                                            Note:
                                                        </Text>
                                                        <View style={{ width: switchMerchant ? '40%' : '30%', }}>
                                                            <TextInput>

                                                            </TextInput>
                                                        </View>
                                                    </View>
                                                </View> */}
                        {/* 20240524 - rack product list */}
                        <View
                          style={{
                            backgroundColor: Colors.whiteColor,
                            width: Dimensions.get('screen').width * 0.87,
                            height: Dimensions.get('screen').height * 0.79,
                            //marginTop: 20,
                            marginHorizontal: 30,
                            marginBottom: 30,
                            alignSelf: 'center',
                            //borderRadius: 5,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderRadius: 5,
                          }}>
                          <KeyboardAwareScrollView
                            nestedScrollEnabled
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={{}}>
                            <View>
                              <View
                                style={{
                                  marginTop: 20,
                                  position: 'absolute',
                                  //backgroundColor: 'red',
                                  alignSelf: 'flex-end',
                                }}></View>
                            </View>

                            <View
                              style={{
                                backgroundColor: '#ffffff',
                                flexDirection: 'row',
                                paddingVertical: 20,
                                paddingHorizontal: 20,
                                marginTop: 10,
                                borderBottomWidth: StyleSheet.hairlineWidth,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                  width: '40%',
                                  alignSelf: 'center',
                                  paddingLeft: '1%',
                                }}>
                                Product Name
                              </Text>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                  width: '21%',
                                  alignSelf: 'center',
                                  marginLeft: 10,
                                }}>
                                SKU
                              </Text>

                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                  width: '18%',
                                  alignSelf: 'center',
                                }}>
                                In Stock
                              </Text>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                  width: '16%',
                                  alignSelf: 'center',
                                }}>
                                Insert Quantity
                              </Text>
                            </View>

                            {renderAddPO()}

                            <View style={{flexDirection: 'row'}}>
                              <View>
                                <TouchableOpacity
                                  style={styles.submitText2}
                                  onPress={() => {
                                    if (outletItems.length > 0) {
                                      setPoItems([
                                        ...poItems,
                                        {
                                          supplyItemId: outletItems[0].uniqueId,
                                          name: outletItems[0].name,
                                          sku: outletItems[0].sku,
                                          skuMerchant:
                                            outletItems[0].skuMerchant,
                                          unit: '',
                                          quantity:
                                            outletItems[0].stockCount || 0, // check if the supply item sku for this outlet existed
                                          insertQuantity: 0,
                                          supplyItem: supplyItems[0],
                                          bc: outletItems[0].bc
                                            ? outletItems[0].bc
                                            : '',
                                        },
                                      ]);
                                    } else {
                                      Alert.alert(
                                        'Error',
                                        'No supplier items added',
                                      );
                                    }
                                  }}>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      alignItems: 'center',
                                    }}>
                                    <Icon1
                                      name="plus-circle"
                                      size={switchMerchant ? 15 : 20}
                                      color={Colors.primaryColor}
                                    />
                                    <Text
                                      style={{
                                        marginLeft: switchMerchant ? 5 : 10,
                                        color: Colors.primaryColor,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}>
                                      Add Product Slot
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </View>

                            <View
                              style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                                justifyContent: 'space-evenly',
                                marginTop: 20,
                                height: 30,
                              }}></View>

                            <View
                              style={{
                                flexDirection: 'row',
                                backgroundColor: '#ffffff',
                                justifyContent: 'center',
                                padding: 18,
                                borderRadius: 5,
                              }}></View>
                          </KeyboardAwareScrollView>
                        </View>
                      </View>
                    </ScrollView>
                  </View>
                </View>
              ) : null}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
      {/* Render QR Modal - 15 Aug 2025 */}
      <ModalComponent
        visible={showQRModal}
        animationType={'slide'}
        transparent={true}
        onRequestClose={() => setDisplayPublishModal(false)}>
        {/*  <View
          style={{
            flex: 1,
            backgroundColor: 'rgba(0,0,0,0.5)',
            justifyContent: 'center',
            padding: 20,
          }}>
          <View
            style={{
              backgroundColor: Colors.whiteColor,
              borderRadius: 8,
              overflow: 'visible',
              padding: 20,
              width: '80%',
              height: '80%',
              alignSelf: 'center',
              position:'relative',
            }}></View>
           Close Button
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 90,
              right: 130,
              padding: 8,
              borderRadius: 20,
              backgroundColor: Colors.lightGrey,
              zIndex: 1,
            }}
            onPress={() => setShowQRModal(false)}>
            <AntDesign name="close" size={20} color={Colors.tabGrey} />
          </TouchableOpacity>
          <Text>QR Code</Text>
        </View>*/}

        <View style={[styles.modalContainer, {}]}>
          <View
            style={[
              styles.modalView,
              {
                width: windowWidth * 0.5,
                height: Dimensions.get('window').height * 0.8,

                padding: Dimensions.get('window').width * 0.03,
                paddingHorizontal: Dimensions.get('window').width * 0.015,

                ...getTransformForModalInsideNavigation(),
              },
            ]}>
            <TouchableOpacity
              style={[
                styles.closeButton,
                switchMerchant
                  ? {
                      position: 'absolute',
                      top: windowWidth * 0.02,
                      right: windowWidth * 0.02,
                    }
                  : {},
              ]}
              onPress={() => {
                setShowQRModal(false);
              }}>
              {switchMerchant ? (
                <AIcon
                  name="closecircle"
                  size={20}
                  color={Colors.fieldtTxtColor}
                />
              ) : (
                <AIcon
                  name="closecircle"
                  size={40}
                  color={Colors.fieldtTxtColor}
                />
              )}
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text
                style={[
                  {
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 22,
                  },
                  switchMerchant
                    ? {
                        fontSize: 16,
                      }
                    : {},
                ]}>
                Rack Number ###
              </Text>
            </View>

            <View style={styles.modalBody}>
              {/*<Text
                style={[
                  {
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 20,

                    marginTop: 15,
                  },
                  switchMerchant
                    ? {
                        fontSize: 14,
                      }
                    : {},
                ]}>
                I am a QR Code
              </Text>*/}
              <QRCode value={'dummyQR'} size={150} />
            </View>

            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',

                flexWrap: 'wrap',
              }}>
              <TouchableOpacity
                style={{
                  justifyContent: 'center',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  backgroundColor: '#0F1A3C',
                  borderRadius: 5,
                  marginLeft: 10,
                  paddingHorizontal: 10,
                  height: 40,
                  alignItems: 'center',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: -1,

                  marginBottom: 15,
                }}
                onPress={async () => {
                  //await printQR();
                }}>
                <Text
                  style={[
                    {
                      color: Colors.whiteColor,

                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                          fontSize: 10,
                        }
                      : {},
                  ]}>
                  PRINT QR
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ModalComponent>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
  },
  content: {
    padding: 16,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    //height: Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : Dimensions.get('screen').height * 0.07,
    height:
      Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : '85%',
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: 'flex-end',
    marginRight: 20,
    marginTop: 15,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: 'flex-end',
    marginRight: 20,
    marginTop: 15,
  },

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    //marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',
    alignSelf: 'flex-end',

    shadowOffset:
      Platform.OS == 'ios'
        ? {
            width: 0,
            height: 0,
          }
        : {
            width: 0,
            height: 7,
          },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset:
      Platform.OS == 'ios'
        ? {
            width: 0,
            height: 0,
          }
        : {
            width: 0,
            height: 7,
          },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 35,
    borderRadius: 5,
    padding: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer1: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView1: {
    // height: 450,
    // width: 500,
    height: Dimensions.get('screen').height * 0.6,
    width: Dimensions.get('screen').width * 0.5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    //justifyContent: 'center'
  },
  modalViewImport: {
    height: Dimensions.get('screen').width * 0.6,
    width: Dimensions.get('screen').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.04,
    top: Dimensions.get('screen').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  textCapacity: {
    backgroundColor: Colors.fieldtBgColor,
    width: 370,
    height: 50,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
  },
});
export default InventoryProductScreen;
